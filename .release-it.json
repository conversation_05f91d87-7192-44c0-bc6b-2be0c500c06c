{"npm": {"publish": false}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "conventionalcommits", "types": [{"type": "feat", "section": "✨新功能"}, {"type": "opt", "section": "🧵优化"}, {"type": "fix", "section": "🐛问题修复"}, {"type": "perf", "section": "🚀提升性能"}, {"type": "refactor", "section": "🏭重构"}, {"type": "chore", "hidden": true}, {"type": "docs", "hidden": true}, {"type": "style", "hidden": true}, {"type": "test", "hidden": true}]}, "infile": "CHANGELOG.md", "ignoreRecommendedBump": true, "strictSemVer": true}}, "git": {"commitMessage": "chore(tag): release v${version}"}}