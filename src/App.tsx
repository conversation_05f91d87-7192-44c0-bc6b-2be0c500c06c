import '@/styles/global.css'
import { router } from '@/configs/router'
import { RouterProvider } from 'react-router-dom'
import { ApolloProvider } from '@apollo/client'
import { client } from '@/graphqls/apollo/client'
import { Toaster } from './components/ui/shad/toaster'
import { useAtom } from 'jotai'
import { scaleAtom } from '@/stores'
import { getScaleStyle, resize } from '@/utils'
import { useEffect } from 'react'

function App() {
  const [scale, setScale] = useAtom(scaleAtom)

  const resizeContent = () => {
    const { scale: initScale } = resize()
    setScale(initScale)
    const baseFontSize = 16
    // 视口宽高
    const windowW = document?.documentElement?.clientWidth
    const windowH = document?.documentElement?.clientHeight
    const radio = windowW / windowH
    const baseWidth = radio > 1 ? 1920 : 1080

    const html = document.documentElement
    const scale = html.clientWidth / baseWidth
    html.style.fontSize = scale * baseFontSize + 'px'
  }
  useEffect(() => {
    resizeContent()
    window.addEventListener('resize', resizeContent)
    return () => {
      window.removeEventListener('resize', resizeContent)
    }
  }, [])
  return (
    <ApolloProvider client={client}>
      <RouterProvider router={router} />
      <Toaster
        duration={2000}
        style={{
          ...getScaleStyle({
            transform: `scale(${scale})  translate(-50%,-50%)`,
            transformOrigin: '0 0',
          }),
        }}
      />
    </ApolloProvider>
  )
}

export default App
