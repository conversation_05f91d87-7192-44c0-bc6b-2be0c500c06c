import React from 'react'
import classNames from 'classnames'
import { MirrorLoading } from 'wujieai-react-icon'

const EventCard: React.FC<{
  data: any
  cardClassNames?: string
  loading?: boolean
}> = ({ data, cardClassNames, loading }) => {
  return (
    <div
      className={classNames(
        'relative rounded-3xl overflow-hidden cursor-pointer maze-event-card-shadow bg-[#0D0531] p-3',
        cardClassNames
      )}
    >
      <div className="maze-event-card-bg rounded-3xl w-full h-full shadow-inner overflow-hidden">
        <img
          className="object-cover w-full h-full border-[6px] rounded-3xl border-[#0D0531]"
          src={data?.cover_image_url}
          alt=""
        />
        <div className="absolute inset-0 bg-[#000] opacity-60"></div>
        <h1 className="absolute text-lg font-bold text-white text-[2.32rem] inset-0 flex items-center justify-center">
          {loading ? (
            <MirrorLoading className="w-12 h-12 animate-spin maze-primary-text" />
          ) : (
            <>{data?.name}</>
          )}
        </h1>
      </div>
    </div>
  )
}

export default EventCard
