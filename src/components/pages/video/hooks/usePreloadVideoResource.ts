import { useRef, useState } from 'react'
import {
  DrawItemFragment,
  GetVideoResourcesDocument,
  GetVideoResourcesQuery,
  GetVideoResourcesQueryVariables,
  VideoResourcePackageFragment,
} from '@/graphqls/types'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'

import {
  graphQLErrorCode,
  preLoadVideos,
  toVideoPoster,
  preLoadImages,
} from '@/utils'
import { allVideoTemplateAtom } from '../videoStore'
import { useSetAtom } from 'jotai'
import { useDevice } from '@/hooks/useDevice'
import { PreLoadSourceObj, PreLoadVideos } from '../source'
import { supportAppPreloadResource } from '@/configs'

/** 预加载视频资源 */
export const usePreloadVideoResource = () => {
  const [loadStatus, setLoadStatus] = useState<
    'loading' | 'success' | 'failed'
  >('loading')
  const loadedRef = useRef(false)
  const [errorText, setErrorText] = useState('')
  const setAllVideoTemplate = useSetAtom(allVideoTemplateAtom)

  const { injectDeviceToken, toAppPreloadResource } = useDevice()

  const getVideoResources = useImperativeQuery<
    GetVideoResourcesQuery,
    GetVideoResourcesQueryVariables
  >(GetVideoResourcesDocument)
  const getAllVideoItem = async (
    resourcePackage: VideoResourcePackageFragment
  ) => {
    const allVideoMap = new Map<string, DrawItemFragment>()

    if (resourcePackage?.categoryVideoList) {
      resourcePackage?.categoryVideoList?.forEach(item => {
        if (item?.itemList) {
          item?.itemList?.forEach(it => {
            if (it.showUrl) {
              allVideoMap.set(it.showUrl, it)
            }
          })
        }
      })
    }

    return [...allVideoMap.values()]
  }
  const fetchResources = async () => {
    setLoadStatus('loading')
    try {
      // 大屏设备参数携带
      injectDeviceToken()
    } catch (error) {
      console.log('error', error)
      setErrorText('您访问的链接无效，请检查链接是否正确')
      setLoadStatus('failed')
      return
    }
    try {
      // 获取 资源包
      const [resourceRes] = await Promise.all([getVideoResources()])

      const resourcePackage =
        resourceRes?.data?.mirrorMarketQuery?.resourcePackage
      const allVideoItem = await getAllVideoItem(
        resourcePackage as VideoResourcePackageFragment
      )

      if (allVideoItem.length > 0) {
        const videos = allVideoItem
          ?.map(it => it.showUrl!)
          .concat(PreLoadVideos)
        const imgs = allVideoItem
          ?.map(it => toVideoPoster(it.showUrl!))
          .concat(toVideoPoster(PreLoadSourceObj.guideVideo))

        if (supportAppPreloadResource()) {
          await toAppPreloadResource([...imgs, ...videos])
        } else {
          // await preLoadImages(imgs)
          await preLoadVideos(videos)
        }
      }
      console.log('allVideoItem', allVideoItem)

      setAllVideoTemplate(allVideoItem)

      setLoadStatus('success')
      loadedRef.current = true
    } catch (error) {
      console.log('error', error)
      if (graphQLErrorCode(error) === 21060001) {
        setErrorText('未检测到资源包，请联系运营人员进行配置以继续使用')
      } else {
        setErrorText('获取资源包失败，请重试')
      }
      setLoadStatus('failed')
    }
  }

  return {
    /** 获取资源包 */
    fetchResources,
    loadStatus,
    errorText,
    /** 是否加载完成 */
    loadedRef,
  }
}
