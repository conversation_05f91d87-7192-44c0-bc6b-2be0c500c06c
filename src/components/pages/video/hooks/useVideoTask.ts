import {
  GetVideoPlayTaskListDocument,
  GetVideoPlayTaskListQuery,
  GetVideoPlayTaskListQueryVariables,
  MirrorAiTaskStatus,
} from '@/graphqls/types'
import { useEffect, useRef } from 'react'
import { useImperativeQuery } from '../../../../hooks/useImperativeQuery'
import { useVideoUtil, UserVideoItem, VideoItemType } from './useVideoUtil'
import { useBridge } from '@/hooks/useBridge'
import { PreLoadSourceObj } from '../source'

/** 视频投屏 */
export const useVideoTask = () => {
  const timerRef = useRef(0)
  /** 用户推流视频队列 */
  const videoUserQueue = useRef<UserVideoItem[]>([])

  const getVideoPlayTaskList = useImperativeQuery<
    GetVideoPlayTaskListQuery,
    GetVideoPlayTaskListQueryVariables
  >(GetVideoPlayTaskListDocument)

  const { reportPlayVideo, changeCurrentPlayingVideo, currentPlayingVideo } =
    useVideoUtil()
  const { postDefaultVideos, postGuideVideo, postUserVideos } = useBridge()

  /** 获取下一个视频 */
  const getNextUserVideo = async () => {
    if (!navigator.onLine) {
      console.log('网络断开:navigator.onLine', navigator.onLine)
      return
    }
    try {
      const res = await getVideoPlayTaskList()
      const nextVideo = res?.data?.mirrorAiTaskQuery?.deviceNextVideoPlay
      // 重复的url排除
      if (
        nextVideo?.videoUrl &&
        !videoUserQueue.current.some(it => it.videoUrl === nextVideo?.videoUrl)
      ) {
        videoUserQueue.current.push(nextVideo)
      }
    } catch (error) {
      console.log('获取下一个视频 接口失败', error)
    } finally {
      // 无论获取失败与否，都出触发下一个视频播放
      playNextVideo()
    }
  }

  /** 播放下一个视频 */
  const playNextVideo = async () => {
    // 正在播放锁
    if (currentPlayingVideo.current) {
      console.log('播放锁,url:', currentPlayingVideo.current.videoUrl)
      return
    }

    console.log(
      'currentPlayingVideo.current-播放锁解除',
      currentPlayingVideo.current
    )
    try {
      const nextTask = videoUserQueue.current.shift()
      if (!nextTask) {
        console.log('没有课播放的素材,nextTask', nextTask)
        return
      }
      changeCurrentPlayingVideo(nextTask)
      playVideo(nextTask)
    } catch (error) {
      console.log(error, 'error')
    }
  }

  /** 视频播放 */
  const playVideo = async (nextTask: VideoItemType) => {
    const videoUrl = nextTask.videoUrl

    // 视频播放开始
    {
      await reportPlayVideo(nextTask, MirrorAiTaskStatus.QUEUEING)
      videoUserQueue.current = videoUserQueue.current.filter(
        it => it.id !== nextTask.id
      )
      getNextUserVideo()
    }

    // app播放用户推送视频
    postUserVideos({
      content: videoUrl,
      callback: async (videoPlayRes: boolean) => {
        if (videoPlayRes) {
          // 视频播放完成
          await reportPlayVideo(nextTask, MirrorAiTaskStatus.SUCCESS)
        } else {
          // 视频播放失败
          await reportPlayVideo(nextTask, MirrorAiTaskStatus.FAIL)
        }
        changeCurrentPlayingVideo()
        playNextVideo()
      },
    })
  }

  useEffect(() => {
    getNextUserVideo()

    // 定时获取 用户视频
    timerRef.current = window.setInterval(() => {
      getNextUserVideo()
    }, 5 * 1000)

    return () => {
      window.clearInterval(timerRef.current)
    }
  }, [])

  const playDefaultVideo = (urls: string[]) => {
    postDefaultVideos({
      content: urls,
    })
    postGuideVideo({
      content: PreLoadSourceObj.guideVideo,
    })
  }
  return {
    /** 播放系统视频 */
    playDefaultVideo,
  }
}
