import {
  useStartVideoPlayTaskMutation,
  useFinishVideoPlayTaskMutation,
  MirrorVideoTaskItemFragment,
  MirrorAiTaskStatus,
} from '@/graphqls/types'

import { useRef } from 'react'

/** 队列中视频类型 */
export enum VideoQueueType {
  /** 默认视频 */
  'default' = 'default',
  /** 用户是视频 */
  'user' = 'user',
}

export type VideoItemType = MirrorVideoTaskItemFragment
export type UserVideoItem = MirrorVideoTaskItemFragment

/** 视频工具类 */
export const useVideoUtil = () => {
  /** 当前正在播放的视频 */
  const currentPlayingVideo = useRef<VideoItemType | null>(null)

  const [reportFinishVideoPlayAction] = useFinishVideoPlayTaskMutation()
  const [reportStartVideoPlayAction] = useStartVideoPlayTaskMutation()

  /** 上报视频状态 */
  const reportPlayVideo = async (
    task: VideoItemType,
    reportStatus: MirrorAiTaskStatus
  ) => {
    switch (reportStatus) {
      case MirrorAiTaskStatus.QUEUEING: {
        // 开始播放
        await reportStartVideoPlayAction({
          variables: {
            taskId: task.id,
          },
        }).catch(() => {})
        break
      }
      case MirrorAiTaskStatus.SUCCESS: {
        // 播放完成
        await reportFinishVideoPlayAction({
          variables: {
            taskId: task.id,
            status: MirrorAiTaskStatus.SUCCESS,
          },
        }).catch(() => {})
        break
      }
      case MirrorAiTaskStatus.FAIL: {
        // 播放失败
        await reportFinishVideoPlayAction({
          variables: {
            taskId: task.id,
            status: MirrorAiTaskStatus.FAIL,
          },
        }).catch(() => {})
        break
      }

      default:
        break
    }
  }
  /** 当前正在播放的视频: 赋值 */
  const changeCurrentPlayingVideo = (task: VideoItemType | null = null) => {
    if (task) {
      // 播放锁
      currentPlayingVideo.current = task
    } else {
      // 解除 播放锁
      currentPlayingVideo.current = null
    }
  }

  return {
    reportPlayVideo,
    changeCurrentPlayingVideo,
    currentPlayingVideo,
  }
}
