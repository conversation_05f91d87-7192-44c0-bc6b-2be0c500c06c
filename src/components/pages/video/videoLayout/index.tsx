import React from 'react'
import classNames from 'classnames'
import { Outlet } from 'react-router-dom'
import PreloadVideoResource from './PreloadVideoResource'
import { AnalyticsScript } from '@/components/business/AnalyticsScript'
import { getScaleStyle } from '@/utils'
import { useAtomValue } from 'jotai'
import { scaleAtom } from '@/stores'

export const VideoLayout: React.FC = () => {
  const scale = useAtomValue(scaleAtom)

  return (
    <>
      <PreloadVideoResource>
        <div
          className={classNames(
            'w-full  flex justify-center items-center relative'
          )}
          style={{
            ...getScaleStyle({
              transform: `scale(${scale})`,
              width: 'auto',
            }),
          }}
        >
          <Outlet />
        </div>
      </PreloadVideoResource>
      <AnalyticsScript />
    </>
  )
}
