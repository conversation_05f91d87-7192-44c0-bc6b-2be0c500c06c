import React, { useEffect } from 'react'
import SourceLoadStatus from '@/components/business/SourceLoadStatus'
import { useAtomValue } from 'jotai'
import { scaleAtom } from '@/stores'
import { getScaleStyle } from '@/utils'
import { usePreloadVideoResource } from '@/components/pages/video/hooks/usePreloadVideoResource'
const PreloadVideoResource: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  const { fetchResources, loadStatus, errorText, loadedRef } =
    usePreloadVideoResource()
  const scale = useAtomValue(scaleAtom)

  useEffect(() => {
    fetchResources()
    setTimeout(
      () => {
        // 未初始化完成, 定时刷新页面
        if (!loadedRef.current) {
          window.location.reload()
        }
        // 副屏基本没有触屏功能，缩短轮询事件
      },
      8 * 60 * 1000
    )
  }, [])

  if (loadStatus !== 'success')
    return (
      <SourceLoadStatus
        loadStatus={loadStatus}
        errorText={errorText}
        style={{
          ...getScaleStyle({
            transform: `scale(${scale})`,
          }),
        }}
        onReload={() => {
          window.location.reload()
        }}
      />
    )

  return <>{children}</>
}
export default PreloadVideoResource
