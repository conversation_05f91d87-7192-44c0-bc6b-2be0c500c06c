import { publicPreLoadSourceObj } from '@/configs/source'
import { screenOrientationAtom } from '@/stores'
import classNames from 'classnames'
import { useAtomValue } from 'jotai'
import { useTranslation } from 'react-i18next'
import { toCDNImage } from '../../../utils/format'

/** 拍照提示 */
function MazePostureTip({ multiple }: { multiple?: boolean }) {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const { t } = useTranslation()
  return (
    <div
      className={classNames(
        'flex  gap-4 mx-auto',
        screenOrientation.isLandScape && 'flex-col'
      )}
    >
      <div className="flex gap-9">
        <img
          src={
            multiple
              ? publicPreLoadSourceObj.correctMultipleImage
              : publicPreLoadSourceObj.correctImage
          }
          className="w-[160px] h-[160px] rounded-2xl"
          alt=""
        />
        <img
          src={
            multiple
              ? publicPreLoadSourceObj.errorMultipleImage
              : publicPreLoadSourceObj.errorImage
          }
          className="w-[160px] h-[160px] rounded-2xl"
          alt=""
        />
      </div>
      <ul className="mt-2">
        {[
          '1 .Face the camera.',
          '2 .No masks or sunglasses.',
          '3.Use good lighting.',
        ].map((item, index) => (
          <li className="flex items-center gap-[12px] mt-2" key={index}>
            <img
              className="w-6 h-6"
              src={toCDNImage('images/icons/check.png')}
            />{' '}
            <span className="maze-primary-text text-[24px]">{t(item)}</span>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default MazePostureTip
