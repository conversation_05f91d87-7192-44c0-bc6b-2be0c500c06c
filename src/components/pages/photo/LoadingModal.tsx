import { MyModal } from '@/components/ui/MyModal'
import { useEffect, useState } from 'react'
import { QuestionModal } from '@/components/business/QuestionModal'
import { useTranslation } from 'react-i18next'
import { useAtomValue } from 'jotai'
import { screenOrientationAtom } from '@/stores'

export const ErrorModal: React.FC<{
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  onRetry: () => void
  errorCount: number /** 生成错误次数 */
}> = ({ open, setOpen, onRetry, errorCount }) => {
  const [questionOpen, setQuestionOpen] = useState(false)
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)

  const onCreate = async () => {
    setOpen(false)
    onRetry()
  }

  useEffect(() => {
    /** 1次生成 + 3次重试，所以当错误次数大于3时，提示 */
    if (errorCount > 3) {
      setOpen(false)
      setQuestionOpen(true)
    }
  }, [errorCount])

  return (
    <>
      <MyModal
        title="生成出错"
        open={open}
        onCancel={() => setOpen(false)}
        width={screenOrientation.isLandScape ? 600 : '90vw'}
        content={
          <div className="text-[2.5rem] text-center text-neutral-400 font-bold leading-[4.2rem]">
            {t('生成过程中出现了一点小问题')},
            <br /> {t('请重新生成')}
          </div>
        }
        onOk={onCreate}
        okText={t('重新生成')}
        showCancelButton={false}
      />
      <QuestionModal open={questionOpen} setOpen={setQuestionOpen} />
    </>
  )
}
