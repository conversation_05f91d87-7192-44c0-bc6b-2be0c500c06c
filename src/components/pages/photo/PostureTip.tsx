import { publicPreLoadSourceObj } from '@/configs/source'
import { AiTaskType } from '@/graphqls/types'
import { screenOrientationAtom, taskTypeAtom } from '@/stores'
import classNames from 'classnames'
import { useAtom, useAtomValue } from 'jotai'
import { useTranslation } from 'react-i18next'

/** 拍照提示 */
function PostureTip({ multiple }: { multiple?: boolean }) {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [taskType] = useAtom(taskTypeAtom)
  const { t } = useTranslation()
  return (
    <div
      className={classNames(
        'flex  gap-8 mx-auto',
        screenOrientation.isLandScape && 'flex-col'
      )}
    >
      <div
        className={classNames(
          'w-[378px] px-[10px]',
          screenOrientation.isPortrait && 'px-12'
        )}
      >
        <div className="flex justify-center my-4">
          <img
            src={
              multiple
                ? publicPreLoadSourceObj.correctMultipleImage
                : publicPreLoadSourceObj.correctImage
            }
            className="w-[160px] h-[160px] rounded-md"
            alt=""
          />
        </div>
        <div
          className={classNames(
            'text-[20px] font-normal leading-9 maze-primary-text',
            screenOrientation.isPortrait && 'text-[16px]'
          )}
        >
          <span className="font-bold">{t('正确')}：</span>
          {multiple
            ? t(
                '确保双人面部皆正对镜头，眼睛看向摄像头，头部不倾斜，五官清晰无遮挡，表情自然。'
              )
            : t(
                '确保拍摄时面部正对镜头，眼睛看向摄像头，五官清晰无遮挡，表情自然，衣服上无人脸图案。'
              )}
        </div>
      </div>
      <div
        className={classNames(
          'w-[378px]  px-[10px]',
          screenOrientation.isPortrait ? 'px-12' : 'mt-12 '
        )}
      >
        <div className="flex justify-center my-4">
          <img
            src={
              multiple
                ? publicPreLoadSourceObj.errorMultipleImage
                : publicPreLoadSourceObj.errorImage
            }
            className="w-[160px] h-[160px] rounded-md"
            alt=""
          />
        </div>
        {taskType === AiTaskType.DRAW &&
          multiple &&
          !screenOrientation.isPortrait && (
            <div className="p-2 bg-[#FCDAE2] rounded-[8px] text-error text-base font-bold text-center my-4 maze-primary-text">
              示例模板站位：女(左) - 男(右)，
              <br />
              照片站位：男(左) - 女(右)，与模板相反
            </div>
          )}
        <div
          className={classNames(
            'text-[20px] font-normal leading-9 maze-primary-text',
            screenOrientation.isPortrait && 'text-[16px] '
          )}
        >
          <span className="font-bold">{t('错误')}：</span>
          {multiple
            ? t(
                '照片人像站位与模板人像站位相反；使用侧脸，面部被口罩、眼镜、帽子等遮挡，且照片中含有干扰物品。'
              )
            : t(
                '使用侧脸，面部被口罩、眼镜、帽子等遮挡，且照片中含有干扰物品。'
              )}
        </div>
      </div>
    </div>
  )
}

export default PostureTip
