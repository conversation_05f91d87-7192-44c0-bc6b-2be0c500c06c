import { MyImage } from '@/components/ui/MyImage'
import { userUploadImageAtom } from '@/stores'
import { toCDNImage } from '@/utils'
import { useAtom } from 'jotai'
import { useTranslation } from 'react-i18next'

type Props = {
  /** 自定义tip组件 */
  renderTipItem?: () => React.ReactNode | null
}
function CustomPhotoModule({ renderTipItem }: Props) {
  const [image] = useAtom(userUploadImageAtom)
  const { t } = useTranslation()

  return (
    <>
      {image ? (
        <div className="rounded-md overflow-hidden w-full h-full">
          <MyImage
            tag="v800"
            src={image}
            className="w-full"
            isAppCache={false}
          />
        </div>
      ) : (
        <div className="border-[8px] border-light-primary bg-white w-[576px] h-[768px] flex items-center justify-center rounded-md">
          {typeof renderTipItem === 'function' ? (
            renderTipItem()
          ) : (
            <div className="text-center">
              <img
                className="w-24 h-24 mb-6"
                src={toCDNImage('/images/common/upload.png')}
                alt=""
              />
              <div className="text-neutral-50 text-lg font-bold">
                {t('请使用微信扫描右侧二维码')}
                <br />
                {t('上传您手机中的图片')}
              </div>
            </div>
          )}
        </div>
      )}
    </>
  )
}

export default CustomPhotoModule
