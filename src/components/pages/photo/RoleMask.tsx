import { MyImage } from '@/components/ui/MyImage'
import classNames from 'classnames'

export const RoleMask: React.FC<{
  multiple: boolean
  faceUrl?: (string | null)[] | null
}> = ({ multiple, faceUrl }) => {
  return (
    <>
      {faceUrl?.length && (
        <div
          className={classNames(
            'absolute left-0 w-full flex items-center px-6',
            [multiple ? ' justify-center top-10' : 'justify-start top-6']
          )}
        >
          {faceUrl?.[0] && (
            <div className="w-[88px] h-[88px] rounded-full overflow-hidden relative">
              <div className="border-4 border-solid border-neutral-900/[0.32] rounded-full absolute top-0 left-0 w-full h-full"></div>
              <MyImage src={faceUrl?.[0]} />
            </div>
          )}
          {faceUrl?.[1] && (
            <div className="w-[88px] h-[88px] rounded-full overflow-hidden relative ml-[148px]">
              <div className="border-4 border-solid border-neutral-900/[0.32] rounded-full absolute top-0 left-0 w-full h-full"></div>
              <MyImage src={faceUrl?.[1]} />
            </div>
          )}
        </div>
      )}
    </>
  )
}
