export const PhotoBorder: React.FC = () => {
  return (
    <>
      <div className="absolute top-[0] left-0 rounded-tl-[32px] h-[88px] w-[88px] border-[8px] border-light-primary !border-r-transparent !border-b-transparent"></div>
      <div className="absolute top-[0] right-0 rounded-tr-[32px] h-[88px] w-[88px] border-[8px] border-light-primary !border-l-transparent !border-b-transparent"></div>
      <div className="absolute bottom-[0] left-0 rounded-bl-[32px] h-[88px] w-[88px] border-[8px] border-light-primary !border-r-transparent !border-t-transparent"></div>
      <div className="absolute bottom-[0] right-0 rounded-br-[32px] h-[88px] w-[88px] border-[8px] border-light-primary !border-l-transparent !border-t-transparent"></div>
    </>
  )
}
