import { useToast } from '@/components/ui/shad/use-toast'
import {
  MirrorAiTaskStatus,
  TaskCreateType,
  useCreateMirrorAiTaskMutation,
  GetMirrorAiTaskStatusDocument,
  GetMirrorAiTaskStatusQuery,
  GetMirrorAiTaskStatusQueryVariables,
  useRetryMirrorAiTaskMutation,
  MirrorAiTaskDetailOrderFragment,
  FitNumber,
  Direction,
  AiTaskType,
} from '@/graphqls/types'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { taskTypeAtom } from '@/stores'
import { MyMirrorAiTask } from '@/stores/types'
import { graphQLErrorMessage } from '@/utils'
import { useAtom } from 'jotai'
import { useRef } from 'react'
import { useTranslation } from 'react-i18next'

export const useAiTask = () => {
  const [taskType] = useAtom(taskTypeAtom)

  const [createMirrorAiTaskAction] = useCreateMirrorAiTaskMutation()
  const getMirrorAiTaskStatusQuery = useImperativeQuery<
    GetMirrorAiTaskStatusQuery,
    GetMirrorAiTaskStatusQueryVariables
  >(GetMirrorAiTaskStatusDocument)
  const [retryMirrorAiTaskAction] = useRetryMirrorAiTaskMutation()

  const taskStatusTimer = useRef(0)
  const { toast } = useToast()
  const { t } = useTranslation()

  /** 创建AI作画任务 */
  const createAiTask = async ({
    initImage,
    taskCreateType,
    orderId,
  }: {
    initImage: string
    taskCreateType: TaskCreateType
    orderId: number
  }) => {
    // 每次重试都需要重新发起作画任务
    const res = await createMirrorAiTaskAction({
      variables: {
        param: {
          initImage,
          orderId,
          taskCreateType,
          type:
            taskType === AiTaskType.VIDEO ? AiTaskType.VIDEO : AiTaskType.DRAW,
        },
      },
    })

    const taskBaseId =
      res.data?.mirrorAiTaskMutation?.mirrorAiTaskCreate?.baseId

    return taskBaseId
  }

  /** 获取作画任务状态 */
  const pollAiTaskStatus = async ({
    taskBaseId,
    onFail,
    onSuccess,
    onProgress,
  }: {
    taskBaseId: number
    onFail?: () => void
    onSuccess?: ({
      order,
      taskList,
    }: {
      order: MirrorAiTaskDetailOrderFragment
      taskList: MyMirrorAiTask[]
    }) => void
    onProgress?: ({
      order,
      taskList,
    }: {
      order: MirrorAiTaskDetailOrderFragment
      taskList: MyMirrorAiTask[]
    }) => void
  }) => {
    try {
      clearInterval(taskStatusTimer.current)

      const res = await getMirrorAiTaskStatusQuery({
        baseId: taskBaseId,
      })

      if (
        res.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.status ===
          MirrorAiTaskStatus.GENERATEING ||
        res.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.status ===
          MirrorAiTaskStatus.QUEUEING
      ) {
        const list =
          res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.details || []

        onProgress?.({
          order: res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.order || {},
          taskList: list.map(
            (item, i) =>
              ({
                ...item,
                print: i === 0 ? 1 : 0,
                categoryInfo:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.templateInfo
                    ?.categoryInfo || [],
                fitNumber:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.templateInfo
                    ?.fitNumber ?? FitNumber.ONE,
                direction:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.templateInfo
                    ?.direction ?? Direction.VERTICAL,
                support3DModeling:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail
                    ?.support3DModeling,
              }) as MyMirrorAiTask
          ),
        })

        taskStatusTimer.current = window.setInterval(() => {
          pollAiTaskStatus({
            taskBaseId,
            onFail,
            onSuccess,
            onProgress,
          })
        }, 3000)
      } else if (
        res.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.status ===
        MirrorAiTaskStatus.FAIL
      ) {
        clearInterval(taskStatusTimer.current)
        onFail?.()
      } else if (
        res.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.status ===
        MirrorAiTaskStatus.SUCCESS
      ) {
        clearInterval(taskStatusTimer.current)

        const list =
          res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.details || []

        onSuccess?.({
          order: res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.order || {},
          taskList: list.map(
            (item, i) =>
              ({
                ...item,
                print: i === 0 ? 1 : 0,
                categoryInfo:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.templateInfo
                    ?.categoryInfo || [],
                fitNumber:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.templateInfo
                    ?.fitNumber ?? FitNumber.ONE,
                direction:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail?.templateInfo
                    ?.direction ?? Direction.VERTICAL,
                support3DModeling:
                  res?.data?.mirrorAiTaskQuery?.mirrorAiTaskDetail
                    ?.support3DModeling,
              }) as MyMirrorAiTask
          ),
        })
      }
    } catch (error) {
      toast({
        description:
          graphQLErrorMessage(error) || t('作画轮询失败，请稍后重试'),
      })
    }
  }

  const retryAiTask = (baseId: number) => {
    console.log('retryAiTask')
    return retryMirrorAiTaskAction({
      variables: {
        baseId,
      },
    })
  }

  const stopPollAiTaskStatus = () => {
    clearInterval(taskStatusTimer.current)
  }

  return {
    /** 创建AI作画任务 */
    createAiTask,
    /** 重新创建AI作画任务 */
    retryAiTask,
    /** 获取作画任务状态 */
    pollAiTaskStatus,
    /** 终止轮询作画任务状态 */
    stopPollAiTaskStatus,
  }
}
