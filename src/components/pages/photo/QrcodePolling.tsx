import {
  GetUploadPicDocument,
  GetUploadPicQuery,
  GetUploadPicQueryVariables,
} from '@/graphqls/types'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { userUploadImageAtom } from '@/stores'
import { toCDNImage } from '@/utils'
import { cn } from '@/utils/shad'
import { useSetAtom } from 'jotai'
import QRCode from 'qrcode.react'
import { useEffect, useRef } from 'react'

const uri = import.meta.env.VITE_MOBILE_HOST

export const QrcodePolling: React.FC<{
  orderId: number
  uploadKey: string
  callback?: (url?: string) => void
  className?: string
}> = ({ orderId, uploadKey, callback, className }) => {
  const setImage = useSetAtom(userUploadImageAtom)
  const timerRef = useRef(0)

  const getImage = useImperativeQuery<
    GetUploadPicQuery,
    GetUploadPicQueryVariables
  >(GetUploadPicDocument)

  useEffect(() => {
    clearInterval(timerRef.current)
    timerRef.current = window.setInterval(() => {
      getImage({
        orderId: orderId,
        uploadKey: uploadKey,
      }).then(res => {
        const url =
          res?.data?.mirrorAiTaskQuery?.mirrorAiTaskPicUploadResult?.resultUrl
        if (url) {
          setImage(url)
          clearInterval(timerRef.current)
          callback?.(url)
        }
      })
    }, 3000)

    return () => {
      clearInterval(timerRef.current)
    }
  }, [uploadKey, orderId])

  return (
    <div
      className={cn(className, 'shadow-box-neutral p-6 bg-white rounded-2xl')}
    >
      <QRCode
        value={`${uri}/upload-pic?key=${uploadKey}&orderId=${orderId}`}
        size={395}
        fgColor="#000"
        imageSettings={{
          src: toCDNImage('/images/common/upload.png'),
          height: 64,
          width: 64,
          excavate: false,
        }}
      />
    </div>
  )
}
