import styles from '../camera-common.module.css'
import PostureTip from '../PostureTip'
import { useAtom, useAtomValue } from 'jotai'
import {
  drawPayOrderAtom,
  screenOrientationAtom,
  userUploadImageAtom,
} from '@/stores'
import CustomPhotoModule from '../CustomPhotoModule'
import { graphQLErrorMessage, randomRange } from '@/utils'
import { Button } from '@/components/ui/shad/button'
import { MirrorShining, MirrorUpload } from 'wujieai-react-icon'
import { useMemo, useState } from 'react'
import { useAiTask } from '../useAiTask'
import { FitNumber, TaskCreateType } from '@/graphqls/types'
import { useNavigate } from 'react-router-dom'
import { QrcodePolling } from '../QrcodePolling'
import { useDebounce } from '@/hooks/useDebounce'
import { useToast } from '@/components/ui/shad/use-toast'
import { PhotoBorder } from '../PhotoBorder'
import classNames from 'classnames'

const randomKey = randomRange(32, 32)

const PhotoNoCamera = () => {
  const [image, setImage] = useAtom(userUploadImageAtom)
  const [drawPayOrder] = useAtom(drawPayOrderAtom)
  const [uploadKey, setUploadKey] = useState(randomKey)
  const screenOrientation = useAtomValue(screenOrientationAtom)

  const navigator = useNavigate()
  const { toast } = useToast()
  const [createLoading, setCreateLoading] = useState(false)

  const orderId = useMemo(() => {
    return drawPayOrder?.order?.id
  }, [drawPayOrder])

  const { createAiTask } = useAiTask()

  const reUpload = () => {
    console.log('重新上传')
    setImage(undefined)
    setUploadKey(randomRange(32, 32))
  }

  const onCreate = useDebounce(async () => {
    try {
      setCreateLoading(true)
      const taskBaseId = await createAiTask({
        initImage: image!,
        orderId: orderId,
        taskCreateType: TaskCreateType.UPLOAD,
      })
      navigator(`/result?taskBaseId=${taskBaseId}`)
    } catch (error) {
      setCreateLoading(false)
      toast({
        description:
          graphQLErrorMessage(error) || '作画任务创建失败，请稍后重试',
      })
    }
  }, [image, orderId])

  console.log('photograph', uploadKey, orderId)
  return (
    <div className="flex flex-col items-center justify-center h-full text-white">
      <h2>No Camera found</h2>
    </div>
  )

  return (
    <div
      className={classNames(
        styles.container,
        screenOrientation.isPortrait ? 'flex-col pt-[129px]' : 'justify-center'
      )}
    >
      <div
        className={classNames(
          styles.left,
          screenOrientation.isLandScape && 'w-[443px]'
        )}
      >
        <PostureTip
          multiple={drawPayOrder?.order?.item?.fitNumber === FitNumber.TWO}
        />
      </div>
      <div
        className={classNames(
          styles.center,
          screenOrientation.isPortrait && 'mt-[78px] mx-auto mb-[150px]'
        )}
      >
        <PhotoBorder />
        <CustomPhotoModule
          renderTipItem={
            screenOrientation.isPortrait
              ? () => {
                  return (
                    <div>
                      {!image && (
                        <QrcodePolling
                          orderId={orderId}
                          uploadKey={uploadKey}
                          key={uploadKey}
                          className=" !shadow-none"
                        ></QrcodePolling>
                      )}
                      <div className="text-neutral-50 text-lg font-bold text-center mt-6">
                        请使用微信扫描右侧二维码
                        <br />
                        上传您手机中的图片
                      </div>
                    </div>
                  )
                }
              : undefined
          }
        />
      </div>
      <div
        className={classNames(
          styles.right,
          screenOrientation.isLandScape && 'flex-col w-[443px]'
        )}
      >
        {!image ? (
          screenOrientation.isLandScape ? (
            <QrcodePolling
              orderId={orderId}
              uploadKey={uploadKey}
              key={uploadKey}
            ></QrcodePolling>
          ) : null
        ) : (
          <div
            className={classNames(
              'text-center',
              screenOrientation.isPortrait && 'flex gap-12'
            )}
          >
            <Button
              className="mb-12 mx-auto"
              variant="minor"
              size="lg"
              ga-data="retryUpload"
              onClick={reUpload}
            >
              <MirrorUpload size={40} className="text-primary mr-2" />
              重新上传
            </Button>
            <Button
              size="lg"
              className="mb-12 mx-auto flex"
              ga-data="startCreate"
              onClick={onCreate}
              loading={createLoading}
            >
              <MirrorShining size={40} className="text-neutral-900 mr-2" />
              立即生成
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
export default PhotoNoCamera
