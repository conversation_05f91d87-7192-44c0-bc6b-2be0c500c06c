import { MyImage } from '@/components/ui/MyImage'
import { MyModal } from '@/components/ui/MyModal'
import { Button } from '@/components/ui/shad/button'
import { toast } from '@/components/ui/shad/use-toast'
import { publicPreLoadSourceObj } from '@/configs/source'
import {
  Get3dDetailDocument,
  Get3dDetailQuery,
  Get3dDetailQueryVariables,
  MirrorAiTaskStatus,
  ThreeDdetailFragment,
  useGen3dMutation,
  useSave3dMutation,
} from '@/graphqls/types'
import { useDebounce } from '@/hooks/useDebounce'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { defaultPrintIndexAtom, screenOrientationAtom } from '@/stores'
import { MyMirrorAiTask } from '@/stores/types'
import { graphQLErrorMessage, queueTimeShow, toCDNImage } from '@/utils'
import { cn } from '@/utils/shad'
import { useAtom, useAtomValue } from 'jotai'
import { useEffect, useRef, useState } from 'react'
import { Icon3d, MirrorLoading, Warning } from 'wujieai-react-icon'
import { ThreeView } from './ThreeView'
import classNames from 'classnames'

interface Props {
  list: MyMirrorAiTask[]
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function ThreeDModal({ open, onClose, list, onSuccess }: Props) {
  const [defaultPrintIndex] = useAtom(defaultPrintIndexAtom)
  const [activeImg, setActiveImg] = useState(list[defaultPrintIndex].id)

  const [resultId, setResultId] = useState<number>()
  const [detail, setDetail] = useState<ThreeDdetailFragment | null | undefined>(
    null
  )
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [isCreate3d, setIsCreate3d] = useState(false) // 是否生成3d模型

  const [isLoading, setIsLoading] = useState(false)

  const [gen3dAction] = useGen3dMutation()
  const gen3d = useDebounce(async () => {
    // reset
    setDetail(null)

    try {
      setIsLoading(true)
      const res = await gen3dAction({
        variables: {
          param: {
            detailId: activeImg,
          },
        },
      })
      setResultId(res.data?.mirrorAiTaskMutation?.mirrorAiTask3DModeling)
      if (screenOrientation.isPortrait) {
        setIsCreate3d(true)
      }
    } catch (error: any) {
      setIsLoading(false)
      toast({
        description: graphQLErrorMessage(error) || '未知错误，请稍后重试',
      })
    }
  }, [activeImg])

  const [save3dAction] = useSave3dMutation()
  const save3d = useDebounce(async () => {
    try {
      await save3dAction({
        variables: {
          id: resultId,
        },
      })
      onSuccess?.()
      onClose()
    } catch (error: any) {
      toast({
        description: graphQLErrorMessage(error) || '未知错误，请稍后重试',
      })
    }
  }, [resultId])

  const get3dDetailAction = useImperativeQuery<
    Get3dDetailQuery,
    Get3dDetailQueryVariables
  >(Get3dDetailDocument)
  const intervalID = useRef(0)
  const pollFn = async (id?: number) => {
    try {
      clearInterval(intervalID.current)
      if (!id) return

      const res = await get3dDetailAction({
        id: id,
      })
      const status =
        res.data.mirrorAiTaskQuery?.mirrorAiTask3DModelingInfo?.status
      if (
        [MirrorAiTaskStatus.GENERATEING, MirrorAiTaskStatus.QUEUEING].includes(
          status as any
        )
      ) {
        intervalID.current = window.setInterval(() => {
          pollFn(id)
        }, 3000)
        setDetail(res.data.mirrorAiTaskQuery?.mirrorAiTask3DModelingInfo)
      } else if (status === MirrorAiTaskStatus.FAIL) {
        clearInterval(intervalID.current)
        setIsLoading(false)
      } else if (status === MirrorAiTaskStatus.SUCCESS) {
        clearInterval(intervalID.current)
        setDetail(res.data.mirrorAiTaskQuery?.mirrorAiTask3DModelingInfo)
        setIsLoading(false)
      }
    } catch (error) {
      if (screenOrientation.isPortrait) {
        setIsCreate3d(false)
      }
      setIsLoading(false)
      toast({
        description: graphQLErrorMessage(error) || '轮询失败，请稍后重试',
      })
    }
  }
  useEffect(() => {
    pollFn(resultId)

    return () => clearInterval(intervalID.current)
  }, [resultId])

  return (
    <MyModal
      open={open}
      width={screenOrientation.isPortrait ? 742 : 1322}
      className={classNames(
        'p-0 border-none',
        screenOrientation.isPortrait ? 'h-[988px]' : 'h-[800px]'
      )}
      contentClassName="!p-0 border-none"
      content={
        <div
          className={classNames(
            'flex p-0 text-neutral-300 font-bold',
            screenOrientation.isPortrait ? 'h-[988px]' : 'h-[800px]'
          )}
        >
          {/* left */}
          <div
            className={classNames(
              'w-[742px] h-full relative p-12',
              isCreate3d && screenOrientation.isPortrait ? 'hidden' : 'block'
            )}
          >
            <div className="w-full h-full pb-32 overflow-y-auto space-y-8">
              <div className="bg-neutral-700 rounded-2xl font-normal p-6 text-[20px] leading-7">
                <div className="flex items-center mb-2 leading-none space-x-2 font-bold">
                  <Warning size={24} />
                  <div>注意</div>
                </div>
                <div></div>
                <ul className=" list-disc list-outside pl-[32px]">
                  <li>点击生成 3D 模型后，将生成一个 GLB 格式的 3D 文件。</li>
                  <li>
                    3D 建模效果不支持直接打印，需保存后通过 “扫码领取电子相册”
                    将 3D 文件下载至本地。
                  </li>
                </ul>
              </div>
              {/* sec-1 */}
              <div>
                <div className="flex items-center mb-4 gap-2">
                  {/* <div className="text-neutral-400 font-bold">01.</div> */}
                  <div className="font-bold text-neutral-50">
                    选择制作 3D 文件的原图
                  </div>
                </div>

                <div className={cn('flex items-center gap-2')}>
                  {list.map(it => {
                    return (
                      <div
                        key={it.id}
                        className={cn(
                          'p-2 cursor-pointer opacity-30 rounded-3xl border-[3px] border-transparent',
                          {
                            'border-primary opacity-100': it.id === activeImg,
                          }
                        )}
                        onClick={() => {
                          setActiveImg(it.id)
                        }}
                      >
                        <div className="relative rounded-2xl overflow-hidden">
                          <MyImage
                            src={it.resultUrl!}
                            className="w-[134px] h-[201px] rounded-2xl"
                            tag="v1200"
                          />
                          {!!it.threeDModelingInfo && (
                            <div className="absolute top-0 right-0 p-1 w-8 h-8 bg-neutral-50/50 rounded-bl-[12px]">
                              <Icon3d className="text-white" size={24} />
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>

            {/* 底部button-横版 */}
            <div className="absolute bottom-12 left-12 right-12">
              {!resultId && (
                <div>
                  <Button size="lg" className="w-full" onClick={gen3d}>
                    生成 3D 模型（GLB 格式）
                  </Button>
                </div>
              )}
              {!!resultId && (
                <div className="">
                  <div className="flex space-x-4 items-center">
                    <Button
                      variant="minor"
                      size="lg"
                      className="w-full mb-4"
                      onClick={gen3d}
                      disabled={isLoading}
                    >
                      重新生成
                    </Button>
                    {!!detail && (
                      <Button
                        size="lg"
                        className="w-full mb-4"
                        onClick={save3d}
                        disabled={isLoading}
                      >
                        保存（GLB 格式）
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* right */}
          <div
            className={classNames(
              'flex items-center justify-center relative',
              screenOrientation.isPortrait
                ? 'w-[742px] h-[786px] justify-start pt-[48px] bg-neutral-700 rounded-3xl'
                : 'w-[580px] h-full',
              !isCreate3d && screenOrientation.isPortrait
                ? 'hidden'
                : 'flex flex-col'
            )}
          >
            <div
              className={classNames(
                'absolute top-0 bottom-0 left-0 right-0 rounded-r-md',
                screenOrientation.isPortrait && 'hidden'
              )}
              style={{
                backdropFilter: 'blur(64px)',
                background: `url(${publicPreLoadSourceObj.blur}) no-repeat center center`,
              }}
            ></div>

            {/* 缺省状态 */}
            {!isLoading &&
              !detail?.modelUrl &&
              screenOrientation.isLandScape && (
                <div className="flex items-center justify-center w-[448px] h-[672px] bg-neutral-900 relative z-10">
                  <div className="flex flex-col items-center">
                    <div className="relative w-[128px] h-[128px] mb-8 text-center">
                      <img
                        src={toCDNImage('/images/common/empty-2.png')}
                        alt=""
                        className="w-full h-full absolute left-0 top-0"
                      />
                    </div>
                    <div className="text-[20px] leading-[28px] font-bold text-center text-neutral-400">
                      请在左侧选择参数
                      <br />
                      生成 3D 效果预览
                    </div>
                  </div>
                </div>
              )}

            {/* loading */}
            {isLoading && (
              <div
                className={classNames(
                  'flex items-center justify-center w-[448px] h-[672px] bg-neutral-900 relative z-10',
                  screenOrientation.isPortrait && 'bg-neutral-700'
                )}
              >
                <div className="flex flex-col items-center">
                  <div className="relative w-[96px] h-[96px] mb-8 text-center">
                    <MirrorLoading className="text-primary w-full h-full absolute left-0 top-0 animate-spin" />
                  </div>
                  <div className="text-[20px] leading-[28px] font-bold text-center">
                    正在生成中
                    <br />
                    预计还需：{queueTimeShow(detail?.expectedTime || 0)}
                  </div>
                </div>
              </div>
            )}

            {/* result */}
            {!isLoading && !!detail?.modelUrl && (
              <div className="flex flex-col items-center space-y-6">
                <div className="relative">
                  <div className="flex items-center justify-center w-[448px] h-[672px] relative z-10 bg-neutral-900">
                    <ThreeView
                      url={detail.modelUrl || ''}
                      onComplete={() => {}}
                      showTips
                      drawBackground={
                        screenOrientation.isPortrait ? '#f4f5f6' : '#ffffff'
                      }
                    />
                  </div>
                </div>
              </div>
            )}
            {screenOrientation.isPortrait && (
              <div
                className={classNames(
                  'absolute -bottom-[156px] left-0 right-0 flex justify-center w-full'
                )}
              >
                <div className="w-full mx-[64px]">
                  <div className="flex space-x-4 items-center">
                    <Button
                      variant="minor"
                      size="lg"
                      className="w-full mb-4"
                      onClick={gen3d}
                      disabled={isLoading || !resultId}
                    >
                      重新生成
                    </Button>
                    <Button
                      size="lg"
                      className="w-full mb-4"
                      onClick={save3d}
                      disabled={isLoading || !detail || !resultId}
                    >
                      保存（GLB 格式）
                    </Button>
                  </div>

                  <p className="text-center text-base opacity-50 font-normal">
                    * 保存 3D 效果后通过扫码领取电子相册获取 3D 文件
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      }
      footer={null}
      footerExtra={
        <div className="absolute left-[50%] translate-x-[-50%]  -bottom-[100px]">
          <Button
            variant="outline"
            size="md"
            className=" w-[256px] mr-6 text-neutral-900"
            onClick={() => {
              onClose()
            }}
          >
            关闭
          </Button>
        </div>
      }
    />
  )
}
