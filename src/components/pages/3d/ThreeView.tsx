import { useRef, useEffect, useState } from 'react'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { Icon3d, MirrorLoading } from 'wujieai-react-icon'

interface IParams {
  node: HTMLDivElement
  url: string
  setCompletePercent: (percentComplete: number) => void
  drawBackground?: string
}
const draw = ({
  node,
  url,
  setCompletePercent,
  drawBackground = '#ffffff',
}: IParams) => {
  const width = node.offsetWidth
  const height = node.offsetHeight
  /** 渲染器 */
  const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  node.appendChild(renderer.domElement)
  /** 相机 */
  const camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 200)
  camera.position.z = 2
  camera.lookAt(0, 0, 0)
  /** 场景 */
  const scene = new THREE.Scene()
  /** 创建背景 */
  scene.background = new THREE.Color(drawBackground)
  /** 创建灯光 */
  const ambientLight = new THREE.AmbientLight(new THREE.Color('#ffffff'), 2.5)
  scene.add(ambientLight)

  const pointLight = new THREE.PointLight(new THREE.Color('#ffffff'))
  camera.add(pointLight)
  scene.add(camera)

  /** 加载模型 */
  const loader = new GLTFLoader()
  loader.load(
    url,
    obj => {
      obj.scene.rotateY(-Math.PI / 2)
      obj.scene.scale.set(1.2, 1.2, 1.2)
      scene.add(obj.scene)
      render()
    },
    xhr => {
      // onProgress
      if (xhr.lengthComputable) {
        const percentComplete = (xhr.loaded / xhr.total) * 100
        setCompletePercent(percentComplete)
      }
    },
    err => {
      console.log('err', err)
    }
  )

  /** 控制器 */
  const controls = new OrbitControls(camera, renderer.domElement)
  controls.addEventListener('change', render)

  controls.minDistance = 1.5
  controls.maxDistance = 2.5
  controls.enablePan = false

  /** 渲染 */
  function render() {
    renderer.render(scene, camera)
  }
  render()

  return {
    // 销毁
    destroy() {
      renderer.dispose()
      node.removeChild(node.children[0])
    },
  }
}

export const ThreeView = ({
  url,
  onComplete,
  showTips,
  drawBackground,
}: {
  url?: string
  onComplete?: () => void
  showTips?: boolean
  drawBackground?: string
}) => {
  const nodeRef = useRef<HTMLDivElement>(null)
  const [completePercent, setCompletePercent] = useState(0)

  useEffect(() => {
    const node = nodeRef.current
    if (!node || !url) return
    setCompletePercent(0)
    const res = draw({
      node,
      url,
      setCompletePercent: num => {
        setCompletePercent(num)
      },
      drawBackground,
    })
    return () => {
      // clear
      res.destroy?.()
    }
  }, [url])

  useEffect(() => {
    if (completePercent >= 100) {
      onComplete?.()
    }
  }, [completePercent])

  return (
    <div className="w-full h-full relative">
      {completePercent < 100 && (
        <div
          className="absolute top-0 left-0 right-0 bottom-0 z-10 flex flex-col items-center justify-center bg-neutral-900"
          style={{ backgroundColor: drawBackground }}
        >
          <div className="flex flex-col items-center justify-center">
            <div className="relative w-[96px] h-[96px] mb-8 text-center">
              <MirrorLoading className="text-primary w-full h-full absolute left-0 top-0 animate-spin" />
            </div>
            <div className="text-[20px] leading-[28px] font-bold text-center">
              加载中，请稍候
            </div>
          </div>
        </div>
      )}
      <div className="relative w-full h-full">
        <div className="w-full h-full" ref={nodeRef} />
        {showTips && (
          <div className="pointer-events-none absolute bottom-7 left-1/2 -translate-x-1/2 py-1.5 px-3 rounded-full bg-neutral-50/10 text-neutral-50 flex items-center space-x-2 font-bold text-sm">
            <Icon3d size={24} />
            <div>拖动旋转模型</div>
          </div>
        )}
      </div>
    </div>
  )
}
