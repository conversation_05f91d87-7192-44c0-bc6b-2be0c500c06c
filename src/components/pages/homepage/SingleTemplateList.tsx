import { AutoScroll, AutoScrollArrow } from '@/components/ui/AutoScroll'
import { useMemo } from 'react'
import SingleTemplate from './SingleTemplate'
import { DrawItemFragment } from '@/graphqls/types'
import { getScaleStyle, toCDNImage } from '@/utils'
import { TemplateListProps } from './const'
import classNames from 'classnames'
import { useTranslation } from 'react-i18next'
import { ThemeDetail } from '@/apis/types'

/** 单行模版列表 */
const SingleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  listKey,
}: TemplateListProps) => {
  const { t } = useTranslation()

  const activeTemplateIndex = useMemo(() => {
    const index = selectTemplateList?.findIndex(
      it => it?.id === activeTemplate?.id
    )
    return index < 0 ? 0 : index
  }, [selectTemplateList, activeTemplate?.id])

  return (
    <div
      className={classNames('relative -mt-[120px] ')}
      style={{
        ...getScaleStyle({
          width: 1920,
        }),
      }}
    >
      <AutoScroll activeIndex={activeTemplateIndex} key={listKey}>
        <div className="flex items-center justify-between px-[128px] p-32">
          {selectTemplateList.length ? (
            <>
              {selectTemplateList.map((it, i) => (
                <SingleTemplate
                  key={i}
                  item={it as ThemeDetail}
                  active={it?.id === activeTemplate?.id}
                  onSelect={() => setActiveTemplate(it)}
                />
              ))}
            </>
          ) : (
            <div className="my-[34px] p-4 rounded-3xl bg-white text-center shadow-box-primary">
              <div className="px-[88px] py-[117px] bg-neutral-700 rounded-2xl">
                <img
                  className="w-24"
                  src={toCDNImage('/images/common/grinning.png')}
                  alt=""
                />
              </div>
              <div className="text-lg font-bold maze-primary-text mt-4">
                {t('当前分类下还没有模板')}
                <br />
                {t('去看看别的分类吧')}
              </div>
            </div>
          )}
        </div>
      </AutoScroll>
      {selectTemplateList.length > 5 && (
        <AutoScrollArrow
          canPrev={activeTemplateIndex !== 0}
          canNext={activeTemplateIndex !== selectTemplateList.length - 1}
          onPrev={() => {
            setActiveTemplate(selectTemplateList[activeTemplateIndex - 1])
          }}
          onNext={() => {
            setActiveTemplate(selectTemplateList[activeTemplateIndex + 1])
          }}
        />
      )}
    </div>
  )
}
export default SingleTemplateList
