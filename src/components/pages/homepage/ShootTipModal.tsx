import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { useNavigate } from 'react-router-dom'
import { useAtomValue } from 'jotai'

import { screenOrientationAtom } from '@/stores'
import { publicPreLoadSourceObj } from '@/configs/source'

import { SvgIcon } from '@/components/ui/SvgIcon'

export const ShootTipModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
}> = ({ open, setOpen }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const screenOrientation = useAtomValue(screenOrientationAtom)

  const onCloseQuestion = () => {
    setOpen?.(false)
    navigate('/photo')
  }

  const tips = [
    {
      icon: publicPreLoadSourceObj.guideYes,
      text: t('正对镜头'),
      tipIcon: '/images/photo/correct.svg',
    },
    {
      icon: publicPreLoadSourceObj.guideNo,
      text: t('移除遮挡'),
      tipIcon: '/images/photo/error.svg',
    },
    {
      icon: publicPreLoadSourceObj.guideLight,
      text: t('光线充足'),
    },
  ]

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 900 : '90vw'}
      content={
        <div className="flex-1 pt-6">
          <div className="flex items-center w-full gap-5">
            {tips.map((tip, index) => (
              <div
                key={index}
                className="flex-1 flex flex-col py-10 h-[30rem] gap-1 items-center text-center border-2 rounded-[2.25rem] maze-bg-gradient-card relative"
              >
                <SvgIcon src={tip.icon} alt="" className="w-full" />
                <p className="text-[26px] leading-[28px] font-semibold maze-primary-text phone:text-[12px] phone:leading-[18px]">
                  {tip.text}
                </p>
                {tip.tipIcon && (
                  <SvgIcon
                    src={tip.tipIcon}
                    alt="提示图标"
                    className="w-16 h-16 absolute top-[1.25rem] right-[1.25rem] phone:scale-75"
                  />
                )}
              </div>
            ))}
          </div>
          <p className="py-12 text-[2rem] text-center font-semibold leading-[2.6rem]">
            {t('拍摄指南')}
          </p>
        </div>
      }
      onOk={onCloseQuestion}
      okText={t('OK')}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => setOpen?.(false)}
    />
  )
}
