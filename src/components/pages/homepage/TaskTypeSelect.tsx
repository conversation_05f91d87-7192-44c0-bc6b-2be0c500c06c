import { useTranslation } from 'react-i18next'
import { publicPreLoadSourceObj } from '@/configs/source'
import { AiTaskType } from '@/graphqls/types'
import { useEffect, useRef } from 'react'
// import { isMachine } from '@/utils'
import { useBridge } from '@/hooks/useBridge'
import { IMAGE_CDN } from '@/configs'
import {
  taskTypeAtom,
  isTaskTypeSelectedAtom,
  screenOrientationAtom,
  canShowVideoAtom,
} from '@/stores'
import classNames from 'classnames'
import { useAtom, useAtomValue } from 'jotai'

export const TaskTypeSelect = () => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [, setIsTaskTypeSelected] = useAtom(isTaskTypeSelectedAtom)
  const [, setTaskType] = useAtom(taskTypeAtom)
  const [canShowVideo, setCanShowVideo] = useAtom(canShowVideoAtom)

  const { showVideo, hiddenVideo } = useBridge()

  const videoRef = useRef<HTMLImageElement>(null)

  const { t } = useTranslation()

  const showVideoFn = () => {
    showVideo({
      url: IMAGE_CDN + publicPreLoadSourceObj.enterVideo,
      position: {
        leftTop: [
          videoRef.current?.getBoundingClientRect().left || 0,
          videoRef.current?.getBoundingClientRect().top || 0,
        ],
        rightBottom: [
          videoRef.current?.getBoundingClientRect().right || 0,
          videoRef.current?.getBoundingClientRect().bottom || 0,
        ],
      },
    })
  }

  useEffect(() => {
    setCanShowVideo(true)

    return () => {
      setCanShowVideo(false)
      hiddenVideo()
    }
  }, [])

  useEffect(() => {
    if (canShowVideo) {
      showVideoFn()
    } else {
      hiddenVideo()
    }
  }, [canShowVideo])

  useEffect(() => {
    if (videoRef.current?.getBoundingClientRect?.()?.left) {
      videoRef.current.onload = () => {
        showVideoFn()
      }
    }
  }, [videoRef.current])

  return (
    <>
      <div
        className={classNames(
          'w-full h-full flex items-center justify-center',
          screenOrientation.isPortrait
            ? 'flex-col space-y-[48px]'
            : 'space-x-[48px]'
        )}
      >
        <div
          className="w-[760px] h-[520px] p-[24px] rounded-[24px] bg-neutral-900 text-center shadow-box-primary"
          onClick={() => {
            setTaskType(AiTaskType.DRAW)
            setIsTaskTypeSelected(true)
          }}
        >
          <img
            className="rounded-[24px] mb-[24px]"
            src={publicPreLoadSourceObj.enterPicture}
            alt={t('生成AI照片')}
          />
          <div className="leading-[40px] text-[32px] font-bold text-gradient-primary">
            {t('生成AI照片')}
          </div>
        </div>
        {/* <div
          className="w-[760px] h-[520px] p-[24px] rounded-[24px] bg-neutral-900 text-center shadow-box-primary"
          onClick={() => {
            setTaskType(AiTaskType.VIDEO)
            setIsTaskTypeSelected(true)
          }}
        >
          {isMachine() ? (
            <img
              ref={videoRef}
              className="rounded-[24px] mb-[24px]"
              src={publicPreLoadSourceObj.enterVideoPoster}
            />
          ) : (
            <video
              className="rounded-[24px] mb-[24px]"
              src={publicPreLoadSourceObj.enterVideo}
              poster={publicPreLoadSourceObj.enterVideoPoster}
              autoPlay
              loop
              muted
            />
          )}

          <div className="leading-[40px] text-[32px] font-bold text-gradient-primary">
            {t('生成AI视频')}
          </div>
        </div> */}
      </div>
    </>
  )
}
