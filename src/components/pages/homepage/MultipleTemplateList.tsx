import { AutoScroll, AutoScrollArrow } from '@/components/ui/AutoScroll'
import { useMemo } from 'react'
import { DrawItemFragment } from '@/graphqls/types'
import { getScaleStyle, toCDNImage } from '@/utils'
import { TemplateListProps } from './const'
import classNames from 'classnames'
import SingleTemplate from './SingleTemplate'
import styles from './MultipleTemplateList.module.css'
import { useTranslation } from 'react-i18next'
import { ThemeDetail } from '@/apis/types'
/** 最后一个复制出来的模版id */
const LAST_COPY_ID = -99
/** 多行模版列表 */
const MultipleTemplateList = ({
  selectTemplateList,
  activeTemplate,
  setActiveTemplate,
  listKey,
  activeGender,
}: TemplateListProps) => {
  const { t } = useTranslation()
  const multipleTemplateList = useMemo(() => {
    /** 第一行 */
    const firstRowList: ThemeDetail[] = []
    /** 第二行 */
    const secondRowList: ThemeDetail[] = []
    const templateNum = selectTemplateList?.length || 0
    // 模版数量在2个及以下，只保留第一行
    if (templateNum <= 2) {
      return {
        firstRowList: selectTemplateList,
        secondRowList,
      }
    }

    selectTemplateList?.forEach((it, index) => {
      if (index % 2 === 0) {
        firstRowList.push(it)
      } else {
        secondRowList.push(it)
      }
    })
    // 第二行数量少1个时，复制一个出来。方便样式对齐
    if (secondRowList.length < firstRowList.length) {
      secondRowList.push({
        ...secondRowList[secondRowList.length - 1],
        id: LAST_COPY_ID,
      })
    }
    return {
      firstRowList,
      secondRowList,
    }
  }, [selectTemplateList])

  const activeMultipleTemplateIndex = useMemo(() => {
    const row = activeTemplate?.row || 0
    let index = 0

    if (row === 0) {
      index = multipleTemplateList?.firstRowList?.findIndex(
        it => it?.id === activeTemplate?.id
      )
    } else {
      index = multipleTemplateList?.secondRowList?.findIndex(
        it => it?.id === activeTemplate?.id
      )
    }

    return {
      index,
      row,
    }
  }, [multipleTemplateList, activeTemplate?.id, activeTemplate?.row])

  // Function to handle navigation between templates with row priority
  const handleNavigation = (direction: 'prev' | 'next') => {
    const { row, index } = activeMultipleTemplateIndex
    const firstRowLength = multipleTemplateList.firstRowList.length

    // Skip the last copied item in the second row if it exists
    const actualSecondRowLength = (secondRowList: ThemeDetail[]) => {
      if (
        secondRowList.length > 0 &&
        secondRowList[secondRowList.length - 1].id === LAST_COPY_ID
      ) {
        return secondRowList.length - 1
      }
      return secondRowList.length
    }

    const secondRowActualLength = actualSecondRowLength(
      multipleTemplateList.secondRowList
    )

    if (direction === 'next') {
      // New navigation logic: alternate between first and second row
      if (row === 0) {
        // If we're in the first row, check if there's a corresponding item in the second row
        const correspondingSecondRowIndex =
          index < secondRowActualLength ? index : null

        if (correspondingSecondRowIndex !== null) {
          // If there's a corresponding item in the second row, go to it
          setActiveTemplate({
            ...multipleTemplateList.secondRowList[correspondingSecondRowIndex],
            row: 1,
          })
        } else if (index < firstRowLength - 1) {
          // If there's no corresponding item in the second row but there are more items in the first row
          // Go to the next item in the first row
          setActiveTemplate({
            ...multipleTemplateList.firstRowList[index + 1],
            row: 0,
          })
        }
      } else if (row === 1) {
        // If we're in the second row
        if (index + 1 < firstRowLength) {
          // If there's a next item in the first row, go to it
          setActiveTemplate({
            ...multipleTemplateList.firstRowList[index + 1],
            row: 0,
          })
        } else if (index < secondRowActualLength - 1) {
          // If we're at the end of the first row but there are more items in the second row
          // Go to the next item in the second row
          setActiveTemplate({
            ...multipleTemplateList.secondRowList[index + 1],
            row: 1,
          })
        }
      }
    } else if (direction === 'prev') {
      // Previous button logic remains similar but reversed
      if (row === 0) {
        // If we're in the first row
        if (index > 0) {
          // If we're not at the beginning of the first row
          const correspondingSecondRowIndex = index - 1
          if (
            correspondingSecondRowIndex >= 0 &&
            correspondingSecondRowIndex < secondRowActualLength
          ) {
            // If there's a corresponding previous item in the second row, go to it
            setActiveTemplate({
              ...multipleTemplateList.secondRowList[
                correspondingSecondRowIndex
              ],
              row: 1,
            })
          } else {
            // Otherwise go to the previous item in the first row
            setActiveTemplate({
              ...multipleTemplateList.firstRowList[index - 1],
              row: 0,
            })
          }
        }
      } else if (row === 1) {
        // If we're in the second row
        // Go to the corresponding item in the first row
        if (index < firstRowLength) {
          setActiveTemplate({
            ...multipleTemplateList.firstRowList[index],
            row: 0,
          })
        } else if (index > 0) {
          // If there's no corresponding item in the first row, go to the previous item in the second row
          setActiveTemplate({
            ...multipleTemplateList.secondRowList[index - 1],
            row: 1,
          })
        }
      }
    }
  }

  return (
    <div className={classNames('relative py-6 top-[50%] -translate-y-[50%]')}>
      <AutoScroll
        activeIndex={activeMultipleTemplateIndex.index}
        activeRow={activeMultipleTemplateIndex.row}
        key={listKey}
        className={classNames('!block w-full h-full')}
        isMultiple
      >
        <div className={classNames(styles.list)}>
          <div className={styles.container}>
            {multipleTemplateList.firstRowList.length ? (
              <>
                {multipleTemplateList.firstRowList.map((it, i) => (
                  <div className={styles.itemBox} key={i}>
                    <SingleTemplate
                      isMultiple
                      activeGender={activeGender}
                      item={it as ThemeDetail}
                      active={it?.id === activeTemplate?.id}
                      onSelect={() =>
                        setActiveTemplate({
                          ...it,
                          row: 0,
                        })
                      }
                    />
                  </div>
                ))}
              </>
            ) : (
              ''
            )}
          </div>
        </div>

        <div className={classNames(styles.list)}>
          <div className={styles.container}>
            {multipleTemplateList.secondRowList.length ? (
              <>
                {multipleTemplateList.secondRowList.map((it, i) => (
                  <div className={styles.itemBox} key={i}>
                    <SingleTemplate
                      isMultiple
                      activeGender={activeGender}
                      className={
                        it.id === LAST_COPY_ID ? 'invisible' : 'visible'
                      }
                      item={it as ThemeDetail}
                      active={it?.id === activeTemplate?.id}
                      onSelect={() =>
                        setActiveTemplate({
                          ...it,
                          row: 1,
                        })
                      }
                    />
                  </div>
                ))}
              </>
            ) : (
              ''
            )}
          </div>
        </div>
        {!multipleTemplateList.secondRowList.length &&
          !multipleTemplateList.firstRowList.length && (
            <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2  my-[34px] p-4  text-center ">
              <img
                className="w-24"
                src={toCDNImage('/images/common/grinning.png')}
                alt=""
              />

              <div className=" text-xl font-bold maze-primary-text mt-6">
                {t('当前分类下还没有模板')}
                <br />
                {t('去看看别的分类吧')}
              </div>
            </div>
          )}
      </AutoScroll>
      {selectTemplateList.length > 6 && (
        <AutoScrollArrow
          canPrev={
            // Can go prev if in first row with index > 0 or in second row
            (activeMultipleTemplateIndex.row === 0 &&
              activeMultipleTemplateIndex.index > 0) ||
            activeMultipleTemplateIndex.row === 1
          }
          canNext={
            // Can go next in these scenarios:
            // 1. In first row with corresponding item in second row
            // 2. In first row with more items in first row
            // 3. In second row with more items in first row
            // 4. In second row with more items in second row (excluding the copy item)
            (activeMultipleTemplateIndex.row === 0 &&
              // Has corresponding item in second row
              (activeMultipleTemplateIndex.index <
                (multipleTemplateList.secondRowList.length > 0 &&
                multipleTemplateList.secondRowList[
                  multipleTemplateList.secondRowList.length - 1
                ].id === LAST_COPY_ID
                  ? multipleTemplateList.secondRowList.length - 1
                  : multipleTemplateList.secondRowList.length) ||
                // Or has more items in first row
                activeMultipleTemplateIndex.index <
                  multipleTemplateList.firstRowList.length - 1)) ||
            (activeMultipleTemplateIndex.row === 1 &&
              // Has next item in first row
              (activeMultipleTemplateIndex.index + 1 <
                multipleTemplateList.firstRowList.length ||
                // Or has more items in second row (excluding copy item)
                (activeMultipleTemplateIndex.index <
                  multipleTemplateList.secondRowList.length - 1 &&
                  multipleTemplateList.secondRowList[
                    multipleTemplateList.secondRowList.length - 1
                  ].id !== LAST_COPY_ID)))
          }
          onPrev={() => handleNavigation('prev')}
          onNext={() => handleNavigation('next')}
        />
      )}
    </div>
  )
}
export default MultipleTemplateList
