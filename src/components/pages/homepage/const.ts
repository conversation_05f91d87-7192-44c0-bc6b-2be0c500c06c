import { ThemeDetail } from '@/apis/types'

/** 当前激活模版 */
export type ActiveTemplateItem = ThemeDetail & {
  /** 所在行 */
  row?: number
}

export type TemplateListProps = {
  selectTemplateList: ThemeDetail[]
  activeTemplate: ThemeDetail | undefined | null
  setActiveTemplate: React.Dispatch<
    React.SetStateAction<ThemeDetail | null | undefined>
  >
  listKey: string
  multiline: boolean
  /** Optional custom Swiper parameters that will override the default values */
  swiperProps?: Record<string, any>
  activeGender?: string
}

export const DEFAULT_DRAW_NUM = 4
