import { MyModal } from '@/components/ui/MyModal'
import { useAtomValue } from 'jotai'

import { screenOrientationAtom } from '@/stores'
import { ThemeDetail } from '@/apis/types'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSwr from 'swr'
import { MirrorLoading } from 'wujieai-react-icon'
import classNames from 'classnames'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { MyImage } from '@/components/ui/MyImage'

export const ThemeDetailModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  themeDetail: ThemeDetail | undefined | null
  activeGender?: string
}> = ({ open, setOpen, themeDetail }) => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const { t } = useTranslation()

  const { data: themeDetailData } = useSwr(
    themeDetail?.id ? [themeDetail?.id] : null,
    ([id]) => _ajax.get(_api.theme_detail, { params: { id } })
  )
  const detailList = useMemo(() => {
    return themeDetailData?.data?.data?.itemList
  }, [themeDetailData])

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 1400 : '90vw'}
      title="All"
      content={
        <div className="w-full">
          <h2 className="text-[2.625rem] text-center pb-10">
            {t('模板列表')} ({detailList?.length || 0})
          </h2>
          <div
            className={classNames(
              'w-full overflow-x-auto whitespace-nowrap leading-none text-[0]',
              screenOrientation.isLandScape
                ? 'h-[400px]'
                : 'h-[60vh] whitespace-pre-wrap overflow-y-auto ipad:h-[66dvh]'
            )}
          >
            {!themeDetailData && (
              <div className="flex items-center justify-center w-full h-full">
                <MirrorLoading className="animate-spin maze-primary-text" />
              </div>
            )}
            {detailList && detailList?.length === 0 && (
              <div className="flex items-center justify-center w-full h-full">
                <div className="text-center">
                  <div className="text-[2rem] leading-[2.5rem] font-bold text-ellipsis maze-primary-text opacity-65">
                    no data.
                  </div>
                </div>
              </div>
            )}
            {detailList?.map(
              (
                item: { image: string | undefined; name: string },
                index: number
              ) => (
                <div
                  key={index}
                  className={classNames(
                    'inline-block rounded-sm overflow-hidden mr-4 last:mr-0 relative',
                    screenOrientation.isLandScape
                      ? 'w-[20%]'
                      : 'w-[31.5%] space-y-2 my-2 phone:w-[48.1%] phone:h-[200px]'
                  )}
                  style={{
                    aspectRatio: '0.66',
                  }}
                >
                  <MyImage
                    src={item.image}
                    tag="v800"
                    className="w-full h-full"
                    imgClassName="object-cover inline-block"
                    isAppCache={false}
                  />
                  <h2 className="absolute bottom-0 left-0 right-0 h-16 mt-0 truncate text-center leading-[4rem] text-[1.3rem] font-semibold px-4 text-white maze-theme-title-bg">
                    {item.name}
                  </h2>
                </div>
              )
            )}
          </div>
        </div>
      }
      showOkButton={false}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => setOpen?.(false)}
    />
  )
}
