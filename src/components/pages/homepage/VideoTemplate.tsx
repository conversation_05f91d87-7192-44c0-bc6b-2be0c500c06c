import { DrawItemFragment } from '@/graphqls/types'
import { to800Image, isMachine } from '@/utils'
import classNames from 'classnames'

export const VideoTemplate: React.FC<{
  data: DrawItemFragment
  active: boolean
  onSelect: () => void
  className?: string
  isPortrait?: boolean
}> = ({ data, active, onSelect, className, isPortrait }) => {
  return (
    <div
      className={classNames(
        'relative p-[12px] rounded-[24px] bg-neutral-900 text-center transition-all',
        isPortrait
          ? {
              'scale-[1.6] z-10 shadow-box-small-primary': active,
              'z-0 shadow-box-primary': !active,
            }
          : {
              'scale-[1.386] z-10 shadow-box-small-primary': active,
              'scale-[0.8] z-0 shadow-box-primary': !active,
            },
        className
      )}
      onClick={onSelect}
    >
      {isMachine() ? (
        <img
          className={classNames(
            'w-full object-cover rounded-[18px] mb-[12px]',
            isPortrait ? 'h-[725px] ' : 'h-[324px]'
          )}
          src={to800Image(data.image!)}
        />
      ) : active ? (
        <video
          src={data.coverUrl!}
          className={classNames(
            'w-full object-cover rounded-[18px] mb-[12px]',
            isPortrait ? 'h-[725px] ' : 'h-[324px]'
          )}
          autoPlay
          loop
          muted
          poster={to800Image(data.image!)}
        />
      ) : (
        <img
          className={classNames(
            'w-full object-cover rounded-[18px] mb-[12px]',
            isPortrait ? 'h-[725px] ' : 'h-[324px]'
          )}
          src={to800Image(data.image!)}
        />
      )}
      <div className="leading-[28px] text-[24px] font-bold flex items-center justify-center">
        <span>{data.name}</span>
        <span className="ml-1.5 border-2 border-solid border-primary text-gradient-primary rounded-[6px] px-1.5 py-0.5 text-[14px] leading-[20px]">
          {data.videoDuration}s
        </span>
      </div>
    </div>
  )
}
