import classNames from 'classnames'
import { VideoTemplate } from './VideoTemplate'
import { ActiveTemplateItem } from './const'
import { DrawItemFragment } from '@/graphqls/types'
import { Swiper, SwiperSlide } from 'swiper/react'
import styles from './VideoTemplateList.module.css'
import { type Swiper as SwiperRef } from 'swiper'

// Import Swiper styles
import 'swiper/css'
import { ArrowLongLeft, ArrowLongRight } from 'wujieai-react-icon'
import { EffectCoverflow } from 'swiper/modules'
import 'swiper/css/effect-coverflow'
import { useAtom, useAtomValue, useSetAtom } from 'jotai'
import { throttle } from 'lodash'
import { canShowVideoAtom, screenOrientationAtom } from '@/stores'
import { useBridge } from '@/hooks/useBridge'
import { useEffect } from 'react'
import { ThemeDetail } from '@/apis/types'

export const VideoTemplateList: React.FC<{
  swiperRef: React.MutableRefObject<SwiperRef | undefined>
  activeTemplate: ThemeDetail | null | undefined
  selectTemplateList: ThemeDetail[]
  setActiveTemplate: React.Dispatch<
    React.SetStateAction<ActiveTemplateItem | undefined | null>
  >
}> = ({ selectTemplateList, activeTemplate, setActiveTemplate, swiperRef }) => {
  const screenOrientation = useAtomValue(screenOrientationAtom)

  const [canShowVideo, setCanShowVideo] = useAtom(canShowVideoAtom)

  const { showVideo, hiddenVideo } = useBridge()

  const slidePrev = throttle(
    () => {
      setCanShowVideo(false)
      swiperRef.current?.slidePrev()
    },
    300,
    {
      leading: true,
      trailing: false,
    }
  )

  const slideNext = throttle(
    () => {
      setCanShowVideo(false)
      swiperRef.current?.slideNext()
    },
    300,
    {
      leading: true,
      trailing: false,
    }
  )

  const slideToLoop = throttle(
    (index: number) => {
      setCanShowVideo(false)
      swiperRef.current?.slideToLoop(index, 300)
    },
    300,
    {
      leading: true,
      trailing: false,
    }
  )

  useEffect(() => {
    if (canShowVideo && activeTemplate) {
      console.log('showVideo')
      showVideo({
        url: activeTemplate?.cover_image,
        position: screenOrientation.isLandScape
          ? {
              leftTop: [560, 268],
              rightBottom: [560 + 800, 268 + 450],
            }
          : {
              leftTop: [214, 344],
              rightBottom: [214 + 653, 344 + 1160],
            },
      })
    } else {
      console.log('hiddenVideo')
      hiddenVideo()
    }
  }, [canShowVideo, activeTemplate])

  return (
    <div
      className={classNames(
        'relative -mt-[120px]'
        // screenOrientation.isLandScape && '-mt-[120px]'
      )}
    >
      <div className="relative">
        {/* <div className="fixed z-[999] bg-[#ddd] opacity-75 left-[214px] top-[344px] w-[652.8px] h-[1160px]"></div> */}
        <Swiper
          loop={true}
          allowTouchMove={false}
          effect={'coverflow'}
          grabCursor={true}
          centeredSlides={true}
          slidesPerView={'auto'}
          longSwipes={false}
          modules={[EffectCoverflow]}
          className={classNames(
            screenOrientation.isPortrait
              ? styles.mySwiperPortrait
              : styles.mySwiper
          )}
          onSlideChangeTransitionEnd={() => {
            console.log('slide-transition')
            setCanShowVideo(true)
          }}
          onSlideChange={swiper => {
            setActiveTemplate(selectTemplateList[swiper.realIndex])
          }}
          onSwiper={swiper => {
            swiperRef.current = swiper
          }}
          {...(screenOrientation.isPortrait
            ? {
                spaceBetween: 60,
                coverflowEffect: {
                  rotate: 0,
                  stretch: 0,
                  depth: 0,
                  modifier: 1,
                  slideShadows: false,
                  scale: 1,
                },
              }
            : {
                spaceBetween: 0,
                coverflowEffect: {
                  rotate: 0,
                  slideShadows: false,
                },
              })}
        >
          {selectTemplateList.map((it, index) => (
            <SwiperSlide key={index}>
              {({ isActive }) => (
                <VideoTemplate
                  isPortrait={screenOrientation.isPortrait}
                  active={isActive}
                  key={it.id}
                  data={it}
                  onSelect={() => {
                    if (isActive) return
                    slideToLoop(index)
                  }}
                />
              )}
            </SwiperSlide>
          ))}
        </Swiper>
        {selectTemplateList.length > 3 && (
          <>
            <SlidePrevButton onPrev={slidePrev} />

            <SlideNextButton onNext={slideNext} />
          </>
        )}
      </div>
    </div>
  )
}

const SlideNextButton = ({ onNext }: { onNext: () => void }) => {
  return (
    <div
      className={classNames(
        'right-[48px] bg-gradient-primary',
        styles.navigationBtn
      )}
      onClick={onNext}
    >
      <ArrowLongRight size={32} className="btn-text-color" />
    </div>
  )
}

const SlidePrevButton = ({ onPrev }: { onPrev: () => void }) => {
  return (
    <div
      className={classNames(
        'left-[48px] bg-gradient-primary',
        styles.navigationBtn
      )}
      onClick={onPrev}
    >
      <ArrowLongLeft size={32} className="btn-text-color" />
    </div>
  )
}
