@import 'tailwindcss' reference;

.template {
  @apply w-[490px] scale-[0.8636] transition-all rounded-[3rem] -mx-3;

  @media (min-width: 768px) and (max-width: 1024px) {
    @apply rounded-[2rem];
  }

  &.active {
    @apply mx-3 scale-100;
    border-color: rgba(139, 92, 246, 0.16);
    box-shadow:
      0px 0px 20px 0px rgba(142, 101, 230, 0.4),
      0px 0px 15px 0px rgba(104, 245, 255, 0.8),
      0px 0px 20px 0px #5100ff;
  }
  &.multiple {
    @apply w-[38.88vw] scale-100 mx-0;

    @media (min-width: 768px) and (max-width: 1024px) {
      @apply w-full;
    }

    &.active {
      @apply scale-[1.09];
    }
  }
}
