import { Button } from '@/components/ui/shad/button'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { fenToYuan, graphQLErrorMessage, toCDNImage } from '@/utils'
import classNames from 'classnames'
import QRCode from 'qrcode.react'
import { useCountDown } from '@/hooks/useCountDown'
import { MyModal } from '@/components/ui/MyModal'
import { publicPreLoadSourceObj } from '@/configs/source'
import {
  GetMirrorPrintOrderDocument,
  GetMirrorPrintOrderQuery,
  GetMirrorPrintOrderQueryVariables,
  OrderStatus,
  PrintOrderInput,
  useCancelOrderMutation,
} from '@/graphqls/types'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { useAtom, useAtomValue } from 'jotai'
import {
  printPayOrderAtom,
  printPrice<PERSON>tom,
  resultImages<PERSON>tom,
  screenOrientation<PERSON>tom,
} from '@/stores'
import { usePayOrder } from '@/hooks/usePayOrder'
import { MyMirrorAiTask } from '@/stores/types'
import { useToast } from '@/components/ui/shad/use-toast'
import { POLL_TICK, ORDER_EXPIRE_TIME, ORDER_TYPE } from '@/configs'
import { useValidResourceChange } from '@/hooks/useValidResourceChange'
import { useDebounce } from '@/hooks/useDebounce'
import { MyImage } from '@/components/ui/MyImage'
import { CDNImage } from '@/components/ui/CDNImage'
import { useTranslation } from 'react-i18next'

const PRICE_TEXT = '赠送'

const uri = import.meta.env.VITE_MOBILE_HOST

const PrintPaymentModal: React.FC<{
  visible: boolean
  setVisible: React.Dispatch<React.SetStateAction<boolean>>
  onSuccess?: () => void
  printOrderParam: PrintOrderInput
}> = ({ visible, setVisible, onSuccess, printOrderParam }) => {
  const [printPrice] = useAtom(printPriceAtom)
  const [printOrderInfo] = useAtom(printPayOrderAtom)
  const [resultImages] = useAtom(resultImagesAtom)
  const { second, resetSecond, clearSecond } = useCountDown(
    printOrderInfo.payInfo?.expireSecond || ORDER_EXPIRE_TIME
  )
  const [cancelOrderAction] = useCancelOrderMutation()
  const { createPrintPayOrder } = usePayOrder()
  const [printImages, setPrintImages] = useState<MyMirrorAiTask[]>([])
  const { toast } = useToast()
  const timerRef = useRef(0)
  const { t } = useTranslation()

  const { refetchResource, isResourceChange, ResourceChangeModal } =
    useValidResourceChange()

  const screenOrientation = useAtomValue(screenOrientationAtom)

  const getPaymentDetail = useImperativeQuery<
    GetMirrorPrintOrderQuery,
    GetMirrorPrintOrderQueryVariables
  >(GetMirrorPrintOrderDocument)

  const onReload = useDebounce(async () => {
    try {
      await createPrintPayOrder({
        param: {
          ...printOrderParam,
          orderId: printOrderInfo.order?.drewOrderId,
        },
      })
      resetSecond(printOrderInfo.payInfo?.expireSecond || ORDER_EXPIRE_TIME)
      startPoll()
    } catch (error) {
      if (isResourceChange(error)) {
        await refetchResource()
        setVisible(false)
      } else {
        toast({ description: graphQLErrorMessage(error) })
      }
    }
  }, [printOrderParam, printOrderInfo])
  /**
   * 取消订单
   */
  const handleCancelOrder = useDebounce(async () => {
    try {
      const res = await cancelOrderAction({
        variables: { orderId: printOrderInfo.order?.id },
      })
      if (res.data?.mirrorOrderMutation?.success) {
        setVisible(false)
      }
    } catch (error: any) {
      toast({ description: error.message })
    }
  }, [printOrderInfo])

  const { totalNum, totalPrice } = useMemo(() => {
    const totalNum = printOrderParam.printInfoList?.reduce((p, c) => {
      return p + (c?.num ?? 0)
    }, 0)
    return {
      totalNum,
      totalPrice: printOrderParam.price,
    }
  }, [printOrderParam])

  const handlePrintImages = () => {
    const notSelectImages = resultImages.filter(it => it.print === 0) // 未添加
    const selectedImages = resultImages.filter(it => it.print! > 0) // 已添加
    const firstImage = selectedImages.slice(0, 1).filter(it => it.print! > 1)
    const images = [...firstImage, ...selectedImages, ...notSelectImages].map(
      (it, i) => {
        let price: any = 0
        let print: number | undefined
        if (i === 0) {
          price = PRICE_TEXT
          print = 1
        } else if (i === 1 && firstImage.length > 0) {
          price = `¥ ${fenToYuan(printPrice.sixPrice * (it.print! - 1))}`
          print = it.print! - 1
        } else {
          if (it.print === 0) {
            // 未添加到打印的结果图
            price = 0
          } else {
            price = `¥ ${fenToYuan(printPrice.sixPrice * it.print!)}`
          }
          print = it.print
        }

        return { ...it, price, print }
      }
    )
    return images
  }

  const startPoll = () => {
    timerRef.current = window.setInterval(() => {
      getPaymentDetail({ orderId: printOrderInfo.order?.id })
        .then(res => {
          const _status = res.data?.mirrorOrderQuery?.devicePrintOrder?.status
          if (
            _status &&
            [OrderStatus.PAID, OrderStatus.COMPLETED].includes(_status)
          ) {
            window.clearInterval(timerRef.current)
            setVisible(false)
            onSuccess?.()
          }
        })
        .catch(error => console.log(error))
    }, POLL_TICK * 1000)
  }

  useEffect(() => {
    if (second === 0) {
      window.clearInterval(timerRef.current)
    }
  }, [second])

  useEffect(() => {
    if (!visible) return
    setPrintImages(handlePrintImages())

    resetSecond(printOrderInfo.payInfo?.expireSecond || ORDER_EXPIRE_TIME)
    startPoll()

    return () => {
      window.clearInterval(timerRef.current)
      clearSecond()
    }
  }, [visible, printOrderInfo, resultImages])

  return (
    <>
      <MyModal
        open={visible}
        onCancel={() => setVisible(false)}
        width={screenOrientation.isPortrait ? 978 : 1074}
        footer={null}
        className="h-[768px] p-0 border-none"
        contentClassName="h-[768px] !p-0 border-none"
        content={
          <div className="w-full h-full flex">
            <div
              className="w-[486px] h-full bg-white bg-opacity-[0.56] rounded-[32px] bg-no-repeat bg-cover p-12 flex flex-col"
              style={{
                backgroundImage: `url(${publicPreLoadSourceObj.blur})`,
              }}
            >
              <div className="after:content-[''] after:mt-[26px] after:block after:border-b after:border-neutral-50/[.56] after:opacity-[0.2]">
                <div className="flex">
                  <div className="w-1/3 text-[24px] leading-[32px] font-bold text-neutral-50 opacity-[0.56]">
                    {t('图片')}
                  </div>

                  <div className="w-1/3 text-[24px] text-center leading-[32px] font-bold text-neutral-50 opacity-[0.56]">
                    {t('数量')}
                  </div>
                  <div className=" w-1/3 text-[24px] text-right leading-[32px] font-bold text-neutral-50 opacity-[0.56]">
                    {t('费用')}
                  </div>
                </div>
              </div>

              <div className="overflow-y-auto flex-1 scrollbar-hidden">
                {printImages?.map((item, index) => (
                  <div
                    key={`${item.id}_${index}`}
                    className={classNames(
                      'relative flex items-center mb-[18px] first:mt-[18px]',
                      {
                        ['opacity-[0.32]']: item.print === 0,
                        ["pb-[18px] after:content-[''] after:absolute after:w-full after:bottom-0 after:left-0 after:block after:border-b after:border-neutral-50/[.56] after:opacity-[0.2]"]:
                          (item.price as any) === PRICE_TEXT,
                      }
                    )}
                  >
                    <div className="w-1/3">
                      <div className="w-[80px] h-[80px] border-[3px] border-neutral-900 rounded-[8px]">
                        <MyImage
                          className="w-full h-full rounded-[8px]"
                          src={item?.resultUrl || ''}
                          imgClassName="object-cover"
                          tag="v800"
                        />
                      </div>
                    </div>

                    <div className="w-1/3 text-[24px] text-center leading-[32px] font-bold text-neutral-50">
                      x{item.print}
                    </div>
                    <div className="w-1/3 text-[24px] text-right leading-[32px] font-bold text-neutral-50">
                      {item.price}
                    </div>
                  </div>
                ))}
              </div>

              <div className="before:content-[''] before:mb-[26px] before:block before:border-b before:border-neutral-50/[.56] before:opacity-[0.2]">
                <div className="flex">
                  <div className="w-1/3 text-[24px] leading-[32px] font-bold text-neutral-50 opacity-[0.56]">
                    合计
                  </div>

                  <div className="w-1/3 text-[24px] text-center leading-[32px] font-bold text-neutral-50">
                    x{totalNum}
                  </div>
                  <div className="w-1/3 text-[24px] text-right leading-[32px] font-bold text-neutral-50">
                    ¥ {fenToYuan(totalPrice)}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col items-center justify-center flex-1">
              <div className="text-[32px] font-bold leading-[40px] text-neutral-50 mb-6">
                使用微信扫码支付开始打印
              </div>

              <div className="relative mb-6 p-6">
                <QRCode
                  value={`${uri}/pay/auth?orderId=${printOrderInfo.order?.id}&orderType=${ORDER_TYPE.PRINT}`}
                  size={338}
                  fgColor="#000"
                  imageSettings={{
                    src: toCDNImage('/images/common/wechat.png'),
                    height: 64,
                    width: 64,
                    excavate: false,
                  }}
                />
                {second === 0 && (
                  <div
                    className="absolute top-0 left-0 w-full h-full z-[100]"
                    onClick={onReload}
                  >
                    <CDNImage src="/images/common/expire_qrcode.png" />
                  </div>
                )}
              </div>
              {second > 0 ? (
                <div className="text-[32px] font-bold leading-[40px] text-neutral-50">
                  <span className="text-gradient-primary">{second}s</span>{' '}
                  后二维码过期
                </div>
              ) : (
                <div className="text-[32px] font-bold leading-[40px] text-neutral-50">
                  二维码已过期，请刷新
                </div>
              )}
            </div>
          </div>
        }
        footerExtra={
          <div className="absolute left-[50%] translate-x-[-50%]  -bottom-[100px]">
            <Button
              variant="outline"
              size="md"
              className=" w-[256px] mr-6 text-neutral-900"
              onClick={handleCancelOrder}
            >
              取消支付
            </Button>
          </div>
        }
      />
      <ResourceChangeModal />
    </>
  )
}

export default PrintPaymentModal
