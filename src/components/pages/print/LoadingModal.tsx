import CountDown from '@/components/business/CountDown'
import { MyModal } from '@/components/ui/MyModal'
import { publicPreLoadSourceObj } from '@/configs/source'
import { isIPad, isPad, toCDNImage } from '@/utils'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/shad/button'
import { useAtom, useAtomValue } from 'jotai'
import { phoneAtom, screenOrientationAtom, taskTypeAtom } from '@/stores'
import { useTranslation } from 'react-i18next'

export const LoadingModal: React.FC<{
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}> = ({ open, setOpen }) => {
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  return (
    <>
      <MyModal
        open={open}
        onCancel={() => setOpen(false)}
        width={screenOrientation.isLandScape ? 600 : '90vw'}
        content={
          <div>
            <div className="h-[380px] mx-auto px-8 rounded-2xl overflow-hidden">
              <video
                src={publicPreLoadSourceObj.loadingVideo}
                poster={publicPreLoadSourceObj.loadingVideoPoster}
                muted
                autoPlay
                loop
                className="h-full"
              >
                您的浏览器不支持 video 标签。
              </video>
            </div>
            <div className="maze-text-primary text-center text-[2rem] leading-[40px] mt-6 font-bold">
              {t(isIPad() ? '正在提交打印任务，请稍后' : '打印中，请稍后')}
            </div>
          </div>
        }
        footer={null}
        contentClassName="p-0"
        className="pb-[60px] pt-[60px]  px-0"
      />
    </>
  )
}

export const ErrorModal: React.FC<{
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  onRetry: () => void
  retryCount?: number
  maxRetryCount?: number
}> = ({ open, setOpen, onRetry, retryCount = 0, maxRetryCount = 3 }) => {
  const navigator = useNavigate()
  const [phone] = useAtom(phoneAtom)
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  return (
    <MyModal
      open={open}
      title={retryCount < maxRetryCount ? t('打印失败') : t('机器出现问题')}
      width={screenOrientation.isLandScape ? 600 : '90vw'}
      onOk={() => {
        if (retryCount < maxRetryCount) {
          setOpen(false)
          onRetry()
        } else {
          navigator('/home')
        }
      }}
      okText={retryCount < maxRetryCount ? t('重新打印') : t('知道了')}
      cancelText={t('返回')}
      onCancel={() => {
        navigator('/home')
      }}
      showCancelButton={retryCount < maxRetryCount}
      content={
        retryCount < maxRetryCount ? (
          <div className="text-center leading-relaxed">
            {t('打印好像出错了，请重试')}
            <br />
            {t('若问题始终存在，请联系电话客服')}
            {!!phone && (
              <>
                <br />
                {phone}
              </>
            )}
          </div>
        ) : (
          <div className="text-center leading-relaxed">
            {t('非常抱歉给您带来不便，机器暂时出现问题。')}
            <br />
            {t(
              '请放心，我们将在 1-3 个工作日内处理好您的退款。感谢您的理解和支持。'
            )}
          </div>
        )
      }
      footerExtra={
        retryCount >= maxRetryCount && (
          <div className="text-center text-white font-bold text-[24px] leading-[32px] mt-6">
            <CountDown
              className="text-gradient-primary"
              value={60}
              onFinish={() => {
                navigator('/home')
                setOpen(false)
              }}
            />
            <span className="text-gradient-primary mr-4">s</span>
            {t('后自动返回首页')}
          </div>
        )
      }
    />
  )
}

export const SuccessModal: React.FC<{
  open: boolean
}> = ({ open }) => {
  const navigator = useNavigate()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const { t } = useTranslation()

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 600 : '90vw'}
      className="!p-0"
      contentClassName="!p-0"
      footer={null}
      onCancel={() => {
        navigator('/home')
      }}
      content={
        <div className="flex w-full rounded-md">
          <div className="flex-1 p-12 text-center">
            <div className="font-bold text-center mb-8 text-white">
              <img
                src={toCDNImage('/images/common/popper.png')}
                alt=""
                className="w-24"
              />
              {isIPad() ? (
                <>
                  <div className="my-4 text-lg text-white">
                    {t('打印任务已提交，')}
                    <br />
                    {t('正在打印中，请留意打印机哦！')}
                  </div>
                  <div className="text-base text-white">
                    {t('如果打印失败，可以在系统相册中选择照片重新打印')}
                  </div>
                </>
              ) : isPad() ? (
                <>
                  <div className="my-4 text-lg text-white">
                    {t('打印完成')}
                    <br />
                    {t('请取走您的照片')}
                  </div>
                  <div className="text-base text-white">
                    {t('如果打印失败，可以在系统相册中选择照片重新打印')}
                  </div>
                </>
              ) : (
                <>
                  <div className="my-4 text-xl text-white">
                    {t('打印完成')}
                    <br />
                    {t('请取走您的照片')}
                  </div>
                </>
              )}
            </div>
            <div className="w-full mb-8 mx-auto">
              <Button
                className="w-full"
                size="lg"
                variant="outline"
                onClick={() => {
                  navigator('/home')
                }}
              >
                {t('返回首页')}
              </Button>
            </div>
            <div className="font-bold text-white text-lg">
              {t('系统将在')}
              <span className="mx-2 text-primary">
                <CountDown
                  value={60}
                  onFinish={() => {
                    navigator('/home')
                  }}
                ></CountDown>
                <span>s</span>
              </span>
              {t('后自动返回首页')}
            </div>
          </div>
          {/* <div className="w-[520px] bg-neutral-700 rounded-r-md py-12 px-16">
            <div className="text-white text-[32px] text-center font-bold mb-6">
              <div className="flex items-center justify-center mb-2 ">
                <img
                  src={toCDNImage('/images/common/wechat_round.png')}
                  alt=""
                  className="w-[48px] h-[48px] mr-2"
                />
                <span>{t('微信扫码')}</span>
              </div>
              <h3 className="mb-2">
                {t(
                  taskType === AiTaskType.DRAW
                    ? '领取所有电子照片'
                    : '领取所有视频'
                )}
              </h3>
            </div>
            <div>
              <ClaimQrCode />
            </div>
          </div> */}
        </div>
      }
    />
  )
}
