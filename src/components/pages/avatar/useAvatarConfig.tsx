import {
  GetAvatarConfigDocument,
  GetAvatarConfigQuery,
  GetAvatarConfigQueryVariables,
} from '@/graphqls/types'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { useEffect, useState } from 'react'

export function useAvatarConfig() {
  const [bg, setBg] = useState<Array<{ id: number; label?: string | null }>>([])
  const [size, setSize] = useState<
    Array<{ id: number; label?: string | null }>
  >([])
  const getConfig = useImperativeQuery<
    GetAvatarConfigQuery,
    GetAvatarConfigQueryVariables
  >(GetAvatarConfigDocument)

  useEffect(() => {
    getConfig().then(res => {
      setBg(
        res.data.mirrorAiTaskQuery?.mirrorAiTaskEditResource?.backgroundColor ||
          []
      )
      setSize(res.data.mirrorAiTaskQuery?.mirrorAiTaskEditResource?.size || [])
    })
  }, [])

  return {
    bgOptions: bg,
    sizeOptions: size,
  }
}
