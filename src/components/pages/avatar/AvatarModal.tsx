import { MyImage } from '@/components/ui/MyImage'
import { MyModal } from '@/components/ui/MyModal'
import { Button } from '@/components/ui/shad/button'
import { toast } from '@/components/ui/shad/use-toast'
import { publicPreLoadSourceObj } from '@/configs/source'
import {
  GetAvatarDetailDocument,
  GetAvatarDetailQuery,
  GetAvatarDetailQueryVariables,
  MirrorAiTaskStatus,
  useGenAvatarMutation,
  useSaveAvatarMutation,
} from '@/graphqls/types'
import { useClickOutside } from '@/hooks/useClickOutside'
import { useDebounce } from '@/hooks/useDebounce'
import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { defaultPrintIndexAtom, screenOrientationAtom } from '@/stores'
import { MyMirrorAiTask } from '@/stores/types'
import {
  graphQLErrorMessage,
  preLoadImages,
  to1200Image,
  toCDNImage,
} from '@/utils'
import { cn } from '@/utils/shad'
import { useAtom, useAtomValue } from 'jotai'
import { useEffect, useRef, useState } from 'react'
import { ArrowDown, MirrorLoading } from 'wujieai-react-icon'
import { useSpring, animated } from '@react-spring/web'
import classNames from 'classnames'

interface Props {
  list: MyMirrorAiTask[]
  bgOptions: Array<{ id: number; label?: string | null }>
  sizeOptions: Array<{ id: number; label?: string | null }>
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

export function AvatarModal({
  open,
  onClose,
  list,
  bgOptions,
  sizeOptions,
  onSuccess,
}: Props) {
  const [defaultPrintIndex] = useAtom(defaultPrintIndexAtom)
  const [activeImg, setActiveImg] = useState(list[defaultPrintIndex].id)
  const [color, setColor] = useState(bgOptions[0].id)
  const [size, setSize] = useState(sizeOptions[0].id)
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [isCreateAvatar, setIsCreateAvatar] = useState(false) // 是否生成证件照

  const screenOrientation = useAtomValue(screenOrientationAtom)

  const sizeLabel = sizeOptions.find(it => it.id === size)?.label

  const ref = useClickOutside(() => setDropdownOpen(false))

  const containerRef = useRef<HTMLDivElement>(null)
  const [{ scrollTop }, scrollApi] = useSpring(() => ({
    scrollTop: 0,
    config: {
      tension: 300,
      clamp: true,
    },
  }))

  const [resultId, setResultId] = useState<number>()
  const [previewUrls, setPreviewUrls] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [previewIndex, setPreviewIndex] = useState(1)

  const [genAvatarAction] = useGenAvatarMutation()
  const genAvatar = useDebounce(async () => {
    try {
      setIsLoading(true)
      const res = await genAvatarAction({
        variables: {
          detailId: activeImg,
          param: {
            backgroundColorId: color,
            sizeId: size,
          },
        },
      })
      if (screenOrientation.isPortrait) {
        setIsCreateAvatar(true)
      }
      setResultId(res.data?.mirrorAiTaskMutation?.mirrorAiTaskEdit)
    } catch (error: any) {
      setIsLoading(false)
      toast({
        description: graphQLErrorMessage(error) || '未知错误，请稍后重试',
      })
    }
  }, [activeImg, color, size])

  const [saveAvatarAction] = useSaveAvatarMutation()
  const saveAvatar = useDebounce(async () => {
    try {
      await saveAvatarAction({
        variables: {
          editId: resultId,
        },
      })

      // 预加载打印页面用的图
      !!previewUrls?.[2] &&
        (await preLoadImages([to1200Image(previewUrls?.[2])]))

      onSuccess?.()
      onClose()
    } catch (error: any) {
      toast({
        description: graphQLErrorMessage(error) || '未知错误，请稍后重试',
      })
    }
  }, [resultId, previewUrls])

  const getAvatarDetailAction = useImperativeQuery<
    GetAvatarDetailQuery,
    GetAvatarDetailQueryVariables
  >(GetAvatarDetailDocument)
  const intervalID = useRef(0)
  const pollFn = async (id?: number) => {
    try {
      clearInterval(intervalID.current)
      if (!id) return

      const res = await getAvatarDetailAction({
        editId: id,
      })
      const status = res.data.mirrorAiTaskQuery?.mirrorAiTaskEdit?.status
      if (
        [MirrorAiTaskStatus.GENERATEING, MirrorAiTaskStatus.QUEUEING].includes(
          status as any
        )
      ) {
        intervalID.current = window.setInterval(() => {
          pollFn(id)
        }, 3000)
      } else if (status === MirrorAiTaskStatus.FAIL) {
        clearInterval(intervalID.current)
        setIsLoading(false)
      } else if (status === MirrorAiTaskStatus.SUCCESS) {
        clearInterval(intervalID.current)
        setPreviewUrls(
          (res.data.mirrorAiTaskQuery?.mirrorAiTaskEdit
            ?.resultUrls as string[]) || []
        )
        setIsLoading(false)
      }
    } catch (error) {
      setIsLoading(false)
      if (screenOrientation.isPortrait) {
        setIsCreateAvatar(false)
      }
      toast({
        description: graphQLErrorMessage(error) || '轮询失败，请稍后重试',
      })
    }
  }
  useEffect(() => {
    pollFn(resultId)

    return () => clearInterval(intervalID.current)
  }, [resultId])

  return (
    <MyModal
      open={open}
      width={screenOrientation.isPortrait ? 742 : 1322}
      className={classNames(
        'p-0 border-none',
        screenOrientation.isPortrait ? 'h-[988px]' : 'h-[800px]'
      )}
      contentClassName="!p-0 border-none"
      content={
        <div
          className={classNames(
            'flex p-0 text-neutral-300 font-bold',
            screenOrientation.isPortrait ? 'h-[988px]' : 'h-[800px]'
          )}
        >
          {/* left */}
          <div
            className={classNames(
              'w-[742px] h-full relative p-12',
              isCreateAvatar && screenOrientation.isPortrait
                ? 'hidden'
                : 'block'
            )}
          >
            <animated.div
              className="w-full h-full pb-32 overflow-y-auto space-y-8"
              ref={containerRef}
              scrollTop={scrollTop}
            >
              {/* sec-1 */}
              <div>
                <div className="flex items-center mb-4 gap-2">
                  <div className="text-neutral-400 font-bold">01.</div>
                  <div className="font-bold text-neutral-50">
                    选择制作证件照的原图
                  </div>
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    {list.map(it => {
                      return (
                        <div
                          key={it.id}
                          className={cn(
                            'p-2 cursor-pointer opacity-30 rounded-3xl border-[3px] border-transparent',
                            {
                              'border-primary opacity-100': it.id === activeImg,
                            }
                          )}
                          onClick={() => setActiveImg(it.id)}
                        >
                          <MyImage
                            src={it.resultUrl!}
                            className="w-[134px] h-[201px] rounded-2xl"
                            tag="v1200"
                          />
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
              {/* sec-2 */}
              <div>
                <div className="flex items-center mb-4 gap-2">
                  <div className="text-neutral-400 font-bold">02.</div>
                  <div className="font-bold text-neutral-50">请选择背景色</div>
                </div>
                <div className="flex items-center space-x-4">
                  {bgOptions.map(it => {
                    return (
                      <div
                        key={it.id}
                        className={cn(
                          'border-2 border-input bg-transparent rounded-full py-2 px-6 cursor-pointer',
                          {
                            'active bg-gradient-primary btn-text-color border-none':
                              it.id === color,
                          }
                        )}
                        onClick={() => setColor(it.id)}
                      >
                        {it.label}
                      </div>
                    )
                  })}
                </div>
              </div>
              {/* sec-3 */}
              <div>
                <div className="flex items-center mb-4 gap-2">
                  <div className="text-neutral-400 font-bold">03.</div>
                  <div className="font-bold text-neutral-50">
                    请选择预设尺寸
                  </div>
                </div>
                <div
                  className="relative w-full cursor-pointer select-none"
                  ref={ref}
                >
                  <div
                    className="border-2 border-input rounded-2xl py-3 px-6 flex items-center justify-between"
                    onClick={() => {
                      setDropdownOpen(!dropdownOpen)
                      scrollApi.start({
                        scrollTop: 600,
                        from: { scrollTop: 0 },
                      })
                    }}
                  >
                    <div>{sizeLabel}</div>
                    <ArrowDown size={32} />
                  </div>
                  {/* 由于页面整体进行了缩放，导致传统的dropdown定位不准 */}
                  {dropdownOpen && (
                    <div className="absolute top-full w-full bg-neutral-900 rounded-2xl px-6 py-4 border-1 border-neutral-700 shadow-[0_48px_64px_0_rgba(0,0,0,0.16)] z-10 max-h-[300px] overflow-y-auto">
                      {sizeOptions.map(it => {
                        return (
                          <div
                            key={it.id}
                            className="py-2 hover:opacity-75"
                            onClick={() => {
                              setSize(it.id)
                              setDropdownOpen(false)
                              scrollApi.start({
                                scrollTop: 0,
                                from: { scrollTop: 600 },
                              })
                            }}
                          >
                            {it.label}
                          </div>
                        )
                      })}
                    </div>
                  )}
                </div>
              </div>
            </animated.div>

            {/* 底部button-横版 */}
            <div className="absolute bottom-12 left-12 right-12">
              {!resultId && (
                <div>
                  <Button size="lg" className="w-full" onClick={genAvatar}>
                    生成证件照
                  </Button>
                </div>
              )}
              {!!resultId && (
                <div className="">
                  <div className="flex space-x-4 items-center">
                    <Button
                      variant="minor"
                      size="lg"
                      className="w-full mb-4"
                      onClick={genAvatar}
                      disabled={isLoading}
                    >
                      重新生成
                    </Button>
                    {!!previewUrls.length && (
                      <Button
                        size="lg"
                        className="w-full mb-4"
                        onClick={saveAvatar}
                        disabled={isLoading}
                      >
                        保存证件照
                      </Button>
                    )}
                  </div>

                  <p className="text-center text-base opacity-50 font-normal">
                    * 保存证件照后将使用证件照进行打印
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* right */}
          <div
            className={classNames(
              'flex items-center justify-center relative',
              screenOrientation.isPortrait
                ? 'w-[742px] h-[786px] justify-start pt-[48px] bg-neutral-700 rounded-3xl'
                : 'w-[580px] h-full',
              !isCreateAvatar && screenOrientation.isPortrait
                ? 'hidden'
                : 'flex flex-col'
            )}
          >
            <div
              className={classNames(
                'absolute top-0 bottom-0 left-0 right-0 rounded-r-md',
                screenOrientation.isPortrait && 'hidden'
              )}
              style={{
                backdropFilter: 'blur(64px)',
                background: `url(${publicPreLoadSourceObj.blur}) no-repeat center center`,
              }}
            ></div>

            {/* 缺省状态 */}
            {!isLoading &&
              !previewUrls.length &&
              screenOrientation.isLandScape && (
                <div className="flex items-center justify-center w-[448px] h-[672px] bg-neutral-900 relative z-10">
                  <div className="flex flex-col items-center">
                    <div className="relative w-[128px] h-[128px] mb-8 text-center">
                      <img
                        src={toCDNImage('/images/common/empty-2.png')}
                        alt=""
                        className="w-full h-full absolute left-0 top-0"
                      />
                    </div>
                    <div className="text-[20px] leading-[28px] font-bold text-center text-neutral-400">
                      请在左侧选择参数
                      <br />
                      生成证件照预览
                    </div>
                  </div>
                </div>
              )}

            {/* loading */}
            {isLoading && (
              <div
                className={classNames(
                  'flex items-center justify-center w-[448px] h-[672px] bg-neutral-900 relative z-10',
                  screenOrientation.isPortrait
                    ? 'bg-neutral-700'
                    : 'bg-neutral-900'
                )}
              >
                <div className="flex flex-col items-center">
                  <div className="relative w-[96px] h-[96px] mb-8 text-center">
                    <MirrorLoading className="text-primary w-full h-full absolute left-0 top-0 animate-spin" />
                  </div>
                  <div className="text-[20px] leading-[28px] font-bold text-center">
                    证件照正在处理中
                    <br />
                    请稍候
                  </div>
                </div>
              </div>
            )}

            {/* result */}
            {!!previewUrls.length && !isLoading && (
              <div
                className={classNames(
                  'flex flex-col items-center',
                  screenOrientation.isLandScape && 'space-y-6'
                )}
              >
                <div className="relative">
                  <div className="flex items-center justify-center w-[448px] h-[672px] relative z-10 bg-transparent">
                    <MyImage
                      src={previewUrls[previewIndex]}
                      className="w-[448px] h-[672px] object-contain bg-transparent"
                    />
                  </div>
                </div>
                <div className="z-10 flex items-center justify-center space-x-4">
                  <div
                    className={cn(
                      'bg-white text-black rounded-full select-none py-2 px-4 text-base font-bold cursor-pointer',
                      {
                        'opacity-55': previewIndex === 2,
                      }
                    )}
                    onClick={() => {
                      setPreviewIndex(1)
                    }}
                  >
                    单张文件
                  </div>
                  <div
                    className={cn(
                      'bg-white text-black rounded-full select-none py-2 px-4 text-base font-bold cursor-pointer',
                      {
                        'opacity-55': previewIndex === 1,
                      }
                    )}
                    onClick={() => {
                      setPreviewIndex(2)
                    }}
                  >
                    打印文件
                  </div>
                </div>
              </div>
            )}
            {/* 底部button-竖版 */}
            {screenOrientation.isPortrait && (
              <div
                className={classNames(
                  'absolute -bottom-[156px] left-0 right-0 flex justify-center w-full'
                )}
              >
                <div className="w-full mx-[64px]">
                  <div className="flex space-x-4 items-center">
                    <Button
                      variant="minor"
                      size="lg"
                      className="w-full mb-4"
                      onClick={genAvatar}
                      disabled={isLoading || !resultId}
                    >
                      重新生成
                    </Button>
                    <Button
                      size="lg"
                      className="w-full mb-4"
                      onClick={saveAvatar}
                      disabled={isLoading || !previewUrls.length || !resultId}
                    >
                      保存证件照
                    </Button>
                  </div>

                  <p className="text-center text-base opacity-50 font-normal">
                    * 保存证件照后将使用证件照进行打印
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      }
      footer={null}
      footerExtra={
        <div className="absolute left-[50%] translate-x-[-50%]  -bottom-[100px]">
          <Button
            variant="outline"
            size="md"
            className=" w-[256px] mr-6 text-neutral-900"
            onClick={() => {
              onClose()
            }}
          >
            关闭
          </Button>
        </div>
      }
    />
  )
}
