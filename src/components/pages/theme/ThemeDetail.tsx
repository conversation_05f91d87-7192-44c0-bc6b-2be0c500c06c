import classnames from 'classnames'
import { use<PERSON><PERSON>, useAtomValue } from 'jotai'
import {
  mazeResultImagesAtom,
  screenOrientationAtom,
  selectedGenderAtom,
} from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { useState, useMemo, useRef, useEffect } from 'react'
import classNames from 'classnames'
import { Swiper as SwiperType } from 'swiper/types'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Pagination } from 'swiper/modules'
// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/grid'

import { SvgIcon } from '@/components/ui/SvgIcon'
import { isIPad, isPhone } from '@/utils'
import { CreateBtn } from '../homepage/CreateBtn'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import useSWR from 'swr'
import { MirrorLoading } from 'wujieai-react-icon'
import { MyImage } from '@/components/ui/MyImage'
import { useTranslation } from 'react-i18next'

const SwiperNextButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute right-6 top-[38%] z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slideNext()}
    >
      <SvgIcon
        src="/images/icons/arrow-right.svg"
        alt="下一页"
        className="w-24 h-24"
      />
    </div>
  )
}

const SwiperPrevButton = ({
  swiperRef,
}: {
  swiperRef: React.RefObject<SwiperType | null>
}) => {
  return (
    <div
      className="absolute left-6 top-[38%] z-10 cursor-pointer hover:scale-110 transition-transform duration-300"
      onClick={() => swiperRef.current?.slidePrev()}
    >
      <SvgIcon
        src="/images/icons/arrow-left.svg"
        alt="上一页"
        className="w-24 h-24"
      />
    </div>
  )
}

export const MazeThemeDetail = () => {
  const swiperRef = useRef<SwiperType | null>(null)
  const [isBeginning, setIsBeginning] = useState(true)
  const [isEnd, setIsEnd] = useState(false)
  const [activeSlideIndex, setActiveSlideIndex] = useState(0)
  const [, setResultImages] = useAtom(mazeResultImagesAtom)
  const [screenOrientation] = useAtom(screenOrientationAtom)
  const activeGender = useAtomValue(selectedGenderAtom)
  const { t } = useTranslation()

  const [searchParams] = useSearchParams()
  const themeId = searchParams.get('themeId')

  // 获取主题详情数据
  const { data: themeDetailData, isLoading } = useSWR(
    themeId ? [themeId] : null,
    ([id]) => _ajax.get(_api.theme_detail, { params: { id } })
  )

  const themeDetail = useMemo(() => {
    return themeDetailData?.data?.data
  }, [themeDetailData])

  const detailList = useMemo(() => {
    return themeDetail?.itemList || []
  }, [themeDetail])

  useEffect(() => {
    return () => {
      setResultImages({})
    }
  }, [setResultImages])

  // 处理滑动状态更新
  const handleSlideChange = (swiper: SwiperType) => {
    setIsBeginning(swiper.isBeginning)
    setIsEnd(swiper.isEnd)
    setActiveSlideIndex(swiper.activeIndex)
  }

  const multiline = screenOrientation.isPortrait

  // Default Swiper parameters
  const defaultSwiperProps = useMemo(
    () => ({
      // There are many special requirements and they are not universal. Make modifications with caution
      slidesPerView: multiline ? 1 : 4, // 横屏则展示4个
      spaceBetween: isIPad() ? 32 : isPhone() ? 32 : 40,
      loop: false,
      modules: [Pagination],
      className: 'mySwiper',
      initialSlide: 0,
    }),
    [multiline, detailList?.length, screenOrientation.isPortrait]
  )

  return (
    <>
      <div className="flex items-center justify-center h-full w-full flex-col">
        <h1 className="maze-page-title pb-3">{themeDetail?.name}</h1>
        {isLoading && (
          <div className="flex items-center justify-center w-full h-[400px]">
            <MirrorLoading className="animate-spin maze-primary-text" />
          </div>
        )}
        {/* 主题详情内容 - 幻灯片 */}
        {!isLoading && themeDetail && detailList?.length > 0 && (
          <div
            className={classNames(
              'py-10 w-[86vw] h-auto overflow-hidden',
              screenOrientation.isLandScape
                ? ''
                : 'w-[82vw] px-20 h-auto ipad:w-[72vw] ipad:py-2 ipad:h-auto phone:h-auto phone:py-2 phone:px-0'
            )}
          >
            {detailList.length >= 2 && (
              <>
                <div
                  className={classNames({
                    'hidden pointer-events-none': isBeginning,
                  })}
                >
                  <SwiperPrevButton swiperRef={swiperRef} />
                </div>
                <div
                  className={classNames({
                    'hidden pointer-events-none': isEnd,
                  })}
                >
                  <SwiperNextButton swiperRef={swiperRef} />
                </div>
              </>
            )}
            <Swiper
              {...defaultSwiperProps}
              onSwiper={(swiper: any) => {
                swiperRef.current = swiper
                // 初始化状态
                setIsBeginning(swiper.isBeginning)
                setIsEnd(swiper.isEnd)
                setActiveSlideIndex(swiper.activeIndex)
              }}
              onSlideChange={handleSlideChange}
            >
              {detailList.map(
                (
                  item: { image: string | undefined; name: string },
                  index: number
                ) => (
                  <SwiperSlide
                    key={index}
                    className={classnames([
                      'cursor-pointer self-stretch transition-all rounded-2xl relative bg-black flex-shrink-0 overflow-hidden',
                    ])}
                  >
                    <div
                      className="w-full h-full"
                      style={{ aspectRatio: '0.66' }}
                    >
                      <MyImage
                        src={item.image}
                        tag="v800"
                        className="w-full h-full"
                        imgClassName="object-cover inline-block"
                        isAppCache={false}
                      />
                    </div>
                  </SwiperSlide>
                )
              )}
            </Swiper>
          </div>
        )}

        {/* 缩略图导航 */}
        {!isLoading && themeDetail && detailList?.length > 1 && multiline && (
          <div className="-mt-2 w-[82vw] flex justify-center px-8">
            <div
              className="flex gap-3 p-3 max-w-full overflow-x-auto scrollbar-hide"
              style={{ scrollBehavior: 'smooth' }}
            >
              {detailList.map(
                (
                  item: { image: string | undefined; name: string },
                  index: number
                ) => {
                  const isActive = index === activeSlideIndex

                  return (
                    <div
                      key={index}
                      onClick={() => {
                        swiperRef.current?.slideTo(index)
                      }}
                      className={classNames(
                        'relative cursor-pointer rounded-2xl overflow-hidden transition-all duration-300 flex-shrink-0',
                        'w-24',
                        isActive
                          ? 'ring-1 ring-black ring-opacity-70'
                          : 'opacity-70 hover:opacity-90'
                      )}
                      style={{ aspectRatio: '0.66' }}
                    >
                      <MyImage
                        src={item.image}
                        tag="v400"
                        className="w-full h-full"
                        imgClassName="object-cover inline-block"
                        isAppCache={false}
                      />
                    </div>
                  )
                }
              )}
            </div>
          </div>
        )}
        {/* 无数据状态 */}
        {!isLoading && (!themeDetail || detailList?.length === 0) && (
          <div className="flex items-center justify-center w-full h-[400px]">
            <div className="text-center">
              <div className="text-[2rem] leading-[2.5rem] font-bold text-ellipsis maze-primary-text opacity-65">
                {!themeDetail ? t('主题不存在或已删除') : t('暂无模板数据')}
              </div>
            </div>
          </div>
        )}
        {themeDetail?.desc && (
          <div className="w-[82vw] rounded-3xl my-6 px-6 py-5 maze-primary-text bg-white/20 backdrop-blur-xl text-[2rem] leading-[130%] ipad:text-[1.8rem]">
            <p className="line-clamp-2">{themeDetail?.desc}</p>
          </div>
        )}
        {/* 下一步按钮 */}
        <div className="pt-16">
          <CreateBtn activeTemplate={themeDetail} activeGender={activeGender} />
        </div>
      </div>
    </>
  )
}
