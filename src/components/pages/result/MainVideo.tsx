import { MyImage } from '@/components/ui/MyImage'
import { MirrorAiTaskStatus, MultiScreenDisplayEnum } from '@/graphqls/types'
import { MyMirrorAiTask } from '@/stores/types'
import { cn } from '@/utils/shad'
import { CDNImage } from '@/components/ui/CDNImage'
import { VideoPlayer } from './VideoPlayer'
import { useAtom } from 'jotai'
import { multiScreenDisplayAtom } from '@/stores'
import classNames from 'classnames'

interface Props {
  curImg?: MyMirrorAiTask
  curVideo?: MyMirrorAiTask
  imgClassName?: string
}

export function MainVideo({ curImg, curVideo, imgClassName }: Props) {
  const [multiScreenDisplay] = useAtom(multiScreenDisplayAtom)
  return (
    <div
      className={cn(
        'w-full h-full flex justify-center flex-col text-center rounded-2xl bg-neutral-700 text-[24px] font-bold leading-[32px]',
        {
          '!bg-neutral-900': curImg?.status === MirrorAiTaskStatus.SUCCESS,
        }
      )}
    >
      {curImg?.status === MirrorAiTaskStatus.SUCCESS ? (
        <div className="w-full h-full relative">
          {multiScreenDisplay === MultiScreenDisplayEnum.SAME && (
            <VideoPlayer
              videoUrl={curVideo?.resultUrl!}
              poster={curImg.resultUrl!}
            />
          )}
          {multiScreenDisplay === MultiScreenDisplayEnum.DIFFERENT && (
            <MyImage
              src={curImg?.resultUrl}
              className={classNames('w-full h-full rounded-2xl', imgClassName)}
              isAppCache={false}
            />
          )}
        </div>
      ) : curImg?.status === MirrorAiTaskStatus.FAIL ? (
        <>
          <div className="mb-2">
            <CDNImage
              src="/images/common/<EMAIL>"
              className="w-[64px] h-[64px]"
              alt=""
            />
          </div>
          <div>
            生成失败
            <br />
            请稍后重试
          </div>
        </>
      ) : (
        <>
          {/* 排队中与生成中合并，统一展示进度条 */}
          <CDNImage
            src="/images/common/painting.png"
            className="w-[400px] h-[400px] block mx-auto"
            alt=""
          />
          <div className="mb-2">
            视频正在生成中
            {Math.floor((curImg?.generatingCompletePercent || 0) * 100)}%
          </div>
        </>
      )}
    </div>
  )
}
