import { CDNImage } from '@/components/ui/CDNImage'
import { MyImage } from '@/components/ui/MyImage'
import { Progress } from '@/components/ui/shad/progress'
import { Direction, MirrorAiTaskStatus } from '@/graphqls/types'
import { MyMirrorAiTask } from '@/stores/types'
import { cn } from '@/utils/shad'
import { Icon3d } from 'wujieai-react-icon'

interface Props {
  resultImages: MyMirrorAiTask[]
  direction?: Direction
  defaultPrintIndex: number
  setDefaultPrintIndex: (n: number) => void
  styles: { [key: string]: string }
  className?: string
}

export function ImageDownloadNav({
  resultImages,
  direction,
  defaultPrintIndex,
  setDefaultPrintIndex,
  styles,
  className,
}: Props) {
  return (
    <div
      className={cn([
        direction === Direction.CROSSWISE
          ? styles.photoTabWrapHorizontal
          : styles.photoTabWrap,
        className,
      ])}
    >
      {resultImages.map((it, i) => (
        <div
          key={it.id}
          className={styles.photoDownloadTabItem}
          onClick={() => {
            setDefaultPrintIndex(i)
          }}
        >
          <div
            className={cn(
              'w-full h-full flex justify-center flex-col text-center rounded-2xl font-bold text-[14px] leading-[24px] relative',
              [
                i === defaultPrintIndex
                  ? it.status === MirrorAiTaskStatus.SUCCESS
                    ? 'bg-neutral-900'
                    : 'bg-neutral-700'
                  : it.status === MirrorAiTaskStatus.SUCCESS
                    ? 'bg-neutral-700'
                    : 'bg-neutral-800',
              ]
            )}
          >
            {it.status === MirrorAiTaskStatus.SUCCESS ? (
              <>
                <MyImage
                  src={it.editResultUrls?.[1] ?? it.resultUrl}
                  isAppCache={false}
                  className="w-full rounded-2xl"
                  tag="v1200"
                />
                {!!it.threeDModelingInfo && (
                  <div className="absolute top-0 right-0 flex p-1 w-8 h-8 bg-neutral-50/50 rounded-tr-2xl rounded-bl-xl">
                    <Icon3d className="text-white" size={24} />
                  </div>
                )}
              </>
            ) : it.status === MirrorAiTaskStatus.FAIL ? (
              <>
                <div className="mb-1">
                  <CDNImage
                    src="/images/common/<EMAIL>"
                    className="w-[32px] h-[32px]"
                    alt=""
                  />
                </div>
                <div>生成失败</div>
              </>
            ) : (
              <>
                {/* 排队中与生成中合并，统一展示进度条 */}
                <div className="mb-1">
                  {Math.floor((it.generatingCompletePercent || 0) * 100)}%
                </div>
                <div className="flex justify-center">
                  <Progress
                    className="w-[88px]"
                    value={Math.floor(
                      (it.generatingCompletePercent || 0) * 100
                    )}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
