import {
  defaultPrintIndex<PERSON>tom,
  drawPayOrder<PERSON>tom,
  isOfficialMerchantAtom,
  photoAlbumQrcodeAtom,
  taskTypeAtom,
} from '@/stores'
import { toAppNoCacheTag } from '@/utils'
import { useAtom } from 'jotai'
import QRCode from 'qrcode.react'
import classnames from 'classnames'
import { useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import classNames from 'classnames'
import Cookies from 'js-cookie'
import { DEVICE_TOKEN } from '@/configs'
import { isIPad } from '../../../utils/ua'

interface IProps {
  /**
   * size: sm 小尺寸192px
   */
  size?: string
  url?: string | null | undefined
}
export const DownloadQrCode: React.FC<IProps> = ({ size, url }) => {
  const [drawOrderInfo] = useAtom(drawPayOrderAtom)
  const [isOfficialMerchant] = useAtom(isOfficialMerchantAtom)
  const [defaultPrintIndex] = useAtom(defaultPrintIndexAtom)
  const [taskType] = useAtom(taskTypeAtom)
  const [photoAlbumQrcode] = useAtom(photoAlbumQrcodeAtom)

  const [searchParams] = useSearchParams()
  const taskBaseId = Number(searchParams.get('taskBaseId'))

  const qrCodeUrl = useMemo(() => {
    const token = Cookies.get(DEVICE_TOKEN) || searchParams.get('device-token')
    return (
      url ||
      `${window.location.origin}/download?taskBaseId=${taskBaseId}&device-token=${token}`
    )
  }, [isOfficialMerchant, drawOrderInfo, defaultPrintIndex, taskType, url])

  /** 如果有配置的领取电子相册二维码，则直接显示配置的二维码 */
  if (photoAlbumQrcode) {
    return (
      <div
        className={classnames(
          'qrcode-wrap',
          'p-6 rounded-[16px] bg-neutral-900',
          'shadow-box-neutral'
        )}
      >
        <img
          className={classNames(
            'object-cover',
            size === 'sm' ? 'w-[192px] h-[192px]' : 'w-[348px] h-[348px]'
          )}
          src={toAppNoCacheTag(photoAlbumQrcode)}
        />
      </div>
    )
  }

  return (
    <div
      className={classnames(
        'qrcode-wrap',
        'rounded-[16px] bg-neutral-900',
        'shadow-box-neutral',
        'p-6'
      )}
    >
      <QRCode value={qrCodeUrl} fgColor="#000" size={isIPad() ? 492 : 456} />
    </div>
  )
}
