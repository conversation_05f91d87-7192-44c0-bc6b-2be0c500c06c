import {
  defaultPrintIndex<PERSON>tom,
  drawPayOrder<PERSON>tom,
  isOfficialMerchantAtom,
  photoAlbumQrcodeAtom,
  taskTypeAtom,
} from '@/stores'
import { toCDNImage, toAppNoCacheTag } from '@/utils'
import { useAtom } from 'jotai'
import QRCode from 'qrcode.react'
import classnames from 'classnames'
import { WeappQrCode } from './WeappQrCode'
import { useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'
import { AiTaskType } from '@/graphqls/types'
import classNames from 'classnames'

const uri = import.meta.env.VITE_MOBILE_HOST

interface IProps {
  /**
   * size: sm 小尺寸192px
   */
  size?: string
}
export const ClaimQrCode: React.FC<IProps> = ({ size }) => {
  const [drawOrderInfo] = useAtom(drawPayOrderAtom)
  const [isOfficialMerchant] = useAtom(isOfficialMerchantAtom)
  const [defaultPrintIndex] = useAtom(defaultPrintIndexAtom)
  const [taskType] = useAtom(taskTypeAtom)
  const [photoAlbumQrcode] = useAtom(photoAlbumQrcodeAtom)

  const [searchParams] = useSearchParams()
  const taskBaseId = Number(searchParams.get('taskBaseId'))

  const qrCodeUrl = useMemo(() => {
    return isOfficialMerchant
      ? /** 备注：此为微信小程序绑定关联的网址，可跳转到小程序
         * https://mp.weixin.qq.com/wxamp/devprofile/add_qrcode?prefix=https%3A%2F%2Fmirror-m.wujieai.com%2Fweapp%3Fname%3Dalbum&token=533053688&lang=zh_CN
         */
        `${uri}/weapp?name=${
          taskType === AiTaskType.VIDEO ? 'videoClaim' : 'claim'
        }&bindAuth=${
          drawOrderInfo.order?.bindAuth
        }&index=${defaultPrintIndex}&taskBaseId=${taskBaseId}`
      : `${uri}/bind?bindAuth=${drawOrderInfo.order?.bindAuth}&index=${defaultPrintIndex}`
  }, [isOfficialMerchant, drawOrderInfo, defaultPrintIndex, taskType])

  /** 如果有配置的领取电子相册二维码，则直接显示配置的二维码 */
  if (photoAlbumQrcode) {
    return (
      <div
        className={classnames(
          'qrcode-wrap',
          'p-6 rounded-[16px] bg-neutral-900',
          'shadow-box-neutral'
        )}
      >
        <img
          className={classNames(
            'object-cover',
            size === 'sm' ? 'w-[192px] h-[192px]' : 'w-[348px] h-[348px]'
          )}
          src={toAppNoCacheTag(photoAlbumQrcode)}
        />
      </div>
    )
  }
  /**
   * 为了方便测试
   * 如果是官方商户，直接进小程序电子相册 */
  if (isOfficialMerchant && import.meta.env.MODE === 'test') {
    return <WeappQrCode size={size} />
  }

  return size === 'sm' ? (
    <div
      className={classnames(
        'rounded-[16px] bg-neutral-900',
        'shadow-box-neutral',
        'relative w-[192px] h-[192px] flex justify-center items-center'
      )}
    >
      <QRCode
        value={qrCodeUrl}
        fgColor="#000"
        size={310}
        imageSettings={{
          src: toCDNImage('/images/common/wechat.png'),
          height: 64,
          width: 64,
          excavate: false,
        }}
        className={classNames(size === 'sm' && 'scale-[0.55]')}
      />
    </div>
  ) : (
    <div
      className={classnames(
        'qrcode-wrap',
        'rounded-[16px] bg-neutral-900',
        'shadow-box-neutral',
        'p-6'
      )}
    >
      <QRCode
        value={qrCodeUrl}
        fgColor="#000"
        size={348}
        imageSettings={{
          src: toCDNImage('/images/common/wechat.png'),
          height: 64,
          width: 64,
          excavate: false,
        }}
      />
    </div>
  )
}
