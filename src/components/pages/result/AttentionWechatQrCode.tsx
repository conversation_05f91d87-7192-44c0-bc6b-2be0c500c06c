import { QRCodeSVG } from 'qrcode.react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useState } from 'react'
import { drawPayOrderAtom } from '@/stores'
import IconLinearLoading from '@/components/ui/icons/LinearLoading'
import { useAtom } from 'jotai'
import {
  GetOfficialAccountQrCodeDocument,
  GetOfficialAccountQrCodeQuery,
  GetOfficialAccountQrCodeQueryVariables,
} from '@/graphqls/types'
import classNames from 'classnames'

import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { useCountDown } from '@/hooks/useCountDown'
import { WECHAT_ACCOUNT_EXPIRE_TIME } from '@/configs'
import { toCDNImage } from '@/utils'
import { IconRefresh } from 'wujieai-react-icon'
import styles from './AttentionWechatQrCode.module.css'

/** 二维码状态 */
enum QrCodeStatus {
  /** 加载中 */
  'PENDING' = 'PENDING',
  /** 加载成功 */
  'SUCCESS' = 'SUCCESS',
  /** 加载失败 */
  'ERROR' = 'ERROR',
  /**过期 */
  'EXPIRED' = 'EXPIRED',
}
/** 关注服务号二维码 */
export const AttentionWechatQrCode: React.FC = () => {
  const [drawOrderInfo] = useAtom(drawPayOrderAtom)
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [qrStatus, setQrStatus] = useState<QrCodeStatus>(QrCodeStatus.PENDING)
  const { resetSecond } = useCountDown(WECHAT_ACCOUNT_EXPIRE_TIME, () => {
    setQrStatus(QrCodeStatus.EXPIRED)
  })

  const getAccountQrCodeAction = useImperativeQuery<
    GetOfficialAccountQrCodeQuery,
    GetOfficialAccountQrCodeQueryVariables
  >(GetOfficialAccountQrCodeDocument)

  const getQrCode = async () => {
    try {
      setQrStatus(QrCodeStatus.PENDING)
      const res = await getAccountQrCodeAction({
        bindAuth: drawOrderInfo?.order?.bindAuth,
      })
      const url = res?.data?.deviceQuery?.getOffiaccountQRCode?.url
      const expireSeconds =
        res?.data?.deviceQuery?.getOffiaccountQRCode?.expireSeconds

      url && setQrCodeUrl(url)
      resetSecond(expireSeconds || WECHAT_ACCOUNT_EXPIRE_TIME)
      setQrStatus(QrCodeStatus.SUCCESS)
    } catch (error) {
      setQrStatus(QrCodeStatus.ERROR)
      console.log('qrcode-error', error)
    }
  }

  useEffect(() => {
    getQrCode()
  }, [])

  return (
    <div
      className={classNames(
        'flex p-6 rounded-[16px] bg-neutral-900 text-center relative    text-neutral-50 text-lg font-bold',
        'shadow-box-neutral'
      )}
    >
      <QRCodeSVG
        value={qrCodeUrl}
        size={348}
        fgColor="#000"
        imageSettings={{
          src: toCDNImage('/images/common/wechat.png'),
          height: 64,
          width: 64,
          excavate: false,
        }}
      ></QRCodeSVG>
      {qrStatus === QrCodeStatus.PENDING && (
        <StatusModule onClick={getQrCode}>
          <div className="w-[144px] h-[144px]">
            <IconLinearLoading className={'animate-spin mb-4'} />
            <div>二维码加载中</div>
            <div>请稍后</div>
          </div>
        </StatusModule>
      )}
      {qrStatus === QrCodeStatus.ERROR && (
        <StatusModule onClick={getQrCode}>
          <div className="w-[64px] h-[64px] bg-neutral-600 rounded-full flex justify-center items-center mb-4">
            <IconRefresh className="text-neutral-50" />
          </div>
          <div>二维码加载失败</div>
          <div>请点击重试</div>
        </StatusModule>
      )}
      {qrStatus === QrCodeStatus.EXPIRED && (
        <StatusModule
          className={styles.qrCode}
          style={{
            backgroundImage: `url(${toCDNImage(
              '/images/common/<EMAIL>'
            )})`,
          }}
          onClick={getQrCode}
        >
          <div className="w-[64px] h-[64px] bg-neutral-900 rounded-full flex justify-center items-center mb-4">
            <IconRefresh className="text-neutral-50" />
          </div>
          <div>二维码已过期</div>
          <div>请点击刷新</div>
        </StatusModule>
      )}
    </div>
  )
}

const StatusModule = ({
  onClick,
  style,
  className,
  children,
}: {
  children: React.ReactNode
  onClick?: MouseEventHandler
  style?: React.CSSProperties
  className?: string
}) => {
  return (
    <div
      className={classNames(
        className,
        'rounded-[16px] absolute top-0 left-0 z-100 bg-neutral-900 w-full h-full flex justify-center items-center flex-col '
      )}
      onClick={onClick}
      style={style}
    >
      {children}
    </div>
  )
}
