import classnames from 'classnames'
import styles from './PictureResult.module.css'
import { Button } from '@/components/ui/shad/button'
import { useAtom, useAtomValue } from 'jotai'
import {
  defaultPrintIndexAtom,
  machineInfoAtom,
  printerEnableAtom,
  resultImagesAtom,
  drawPayOrderAtom,
  screenOrientationAtom,
  isSplitPrintingAtom,
  isOfficialMerchantAtom,
  resultOrderAtom,
} from '@/stores'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Icon3d, MirrorPicture, Photograph } from 'wujieai-react-icon'
import { useEffect, useState, useMemo, useRef } from 'react'
import { useAiTask } from '@/components/pages/photo/useAiTask'
import { ErrorModal } from '@/components/pages/photo/LoadingModal'
import { Direction, MirrorAiTaskStatus } from '@/graphqls/types'
import { PrinterStatus } from '@/stores/types'
import { useAvatarConfig } from '@/components/pages/avatar/useAvatarConfig'
import { AvatarModal } from '@/components/pages/avatar/AvatarModal'
import { ThreeDModal } from '@/components/pages/3d/ThreeDModal'
import { MainImage } from '@/components/pages/result/MainImage'
import { ImageNav } from '@/components/pages/result/ImageNav'
import { useBridge } from '@/hooks/useBridge'
import {
  isIPad,
  isNullOrUndefined,
  isPad,
  preLoadImages,
  to1200Image,
  toAppNoCacheTag,
} from '@/utils'
import { toast } from '@/components/ui/shad/use-toast'
import ResultQrcodeComp from '@/components/pages/result/ResultQrcodeComp'
import { useTemplateSplicing } from '@/hooks/useTemplateSplicing'
import { uniq } from 'lodash'
import classNames from 'classnames'
import { useTranslation } from 'react-i18next'

export const PictureResult = ({
  setBackOpen,
}: {
  setBackOpen: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const navigator = useNavigate()
  const [defaultPrintIndex, setDefaultPrintIndex] = useAtom(
    defaultPrintIndexAtom
  )
  const [resultImages, setResultImages] = useAtom(resultImagesAtom)
  const [, setResultOrder] = useAtom(resultOrderAtom)
  const [printerEnable] = useAtom(printerEnableAtom)
  const [isSplitPrinting] = useAtom(isSplitPrintingAtom)
  const [isOfficialMerchant] = useAtom(isOfficialMerchantAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [drawPayOrder] = useAtom(drawPayOrderAtom)
  const [templateLoading, setTemplateLoading] = useState(false)

  const { bgOptions, sizeOptions } = useAvatarConfig()
  const [editOpen, setEditOpen] = useState(false)

  const { downImage } = useBridge()
  const { t } = useTranslation()

  // 3d
  const [threeDOpen, setThreeDOpen] = useState(false)

  const direction = useMemo(() => {
    return drawPayOrder?.order?.item?.direction ?? resultImages?.[0]?.direction
  }, [drawPayOrder, resultImages?.[0]?.direction])

  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])

  const [errorOpen, setErrorOpen] = useState(false)
  const [errorCount, setErrorCount] = useState(0)

  const [searchParams] = useSearchParams()

  const taskBaseId = Number(searchParams.get('taskBaseId'))

  const { pollAiTaskStatus, retryAiTask, stopPollAiTaskStatus } = useAiTask()

  const { imageSplicingResult, loading: loadingImageSplicing } =
    useTemplateSplicing()

  const cacheTemplateResuleImages = useRef<{ id: number; url: string }[]>([])
  const spliciedIds = useRef<number[]>([])

  const pollStatus = () => {
    pollAiTaskStatus({
      taskBaseId,
      onProgress({ taskList }) {
        setResultImages(taskList)
        // if (!isSplitPrinting && isOfficialMerchant) {
        //   imageSplicing(taskList)
        // }
      },
      onFail() {
        setErrorOpen(true)
        setErrorCount(val => val + 1)
      },
      onSuccess({ order, taskList }) {
        setResultImages(taskList)
        setResultOrder(order)
        // if (!isSplitPrinting && isOfficialMerchant) {
        //   imageSplicing(taskList)
        // }
      },
    })
  }

  useEffect(() => {
    pollStatus()

    return () => {
      stopPollAiTaskStatus()
    }
  }, [])
  // 当前聚焦的图片
  const curImg = resultImages?.[defaultPrintIndex]

  const toPrint = async () => {
    if (isPad() || isIPad()) {
      downImage({
        imageUrls: resultImages
          .map(it => {
            return it.editResultUrls?.[1]
              ? [it.resultUrl, it.editResultUrls?.[1]]
              : it.resultUrl
          })
          .flat(),
      })
    }

    setTemplateLoading(true)

    // await awaitSplicing()

    // await preLoadImages(
    //   cacheTemplateResuleImages.current.map(it => to1200Image(it.url!))
    // )

    setResultImages(images => {
      return images.map((it, i) => {
        // const previewUrl = cacheTemplateResuleImages.current?.find(
        //   o => it.id === o.id
        // )?.url
        if (i === defaultPrintIndex) {
          return {
            ...it,
            // previewUrl: previewUrl ?? it.previewUrl,
            print: 1,
          }
        } else {
          return {
            ...it,
            // previewUrl: previewUrl ?? it.previewUrl,
            print: 0,
          }
        }
      })
    })

    navigator(`/print?taskBaseId=${taskBaseId}`)
  }
  return (
    <>
      <div
        className={classNames(
          'flex items-center justify-center h-full',
          screenOrientation.isPortrait && 'flex-col'
        )}
      >
        {screenOrientation?.isPortrait && (
          <ResultQrcodeComp className={styles.customQrcode} />
        )}
        <div
          className={classnames(
            [
              direction === Direction.CROSSWISE
                ? styles.photoHorizontal
                : styles.photo,
            ],
            'shadow-box-neutral',
            screenOrientation.isPortrait && 'mt-[20px] mb-[150px]'
          )}
        >
          <div
            className={classnames([
              direction === Direction.CROSSWISE
                ? styles.photoItemHorizontal
                : styles.photoItem,
            ])}
          >
            <MainImage curImg={curImg} />
          </div>
          {resultImages.length > 1 && (
            <ImageNav
              resultImages={resultImages}
              direction={direction}
              defaultPrintIndex={defaultPrintIndex}
              setDefaultPrintIndex={setDefaultPrintIndex}
              styles={styles}
            />
          )}
        </div>

        {/* 右侧功能栏 */}
        <div
          className={classNames(
            screenOrientation.isPortrait
              ? 'flex justify-center gap-12'
              : 'ml-[200px]'
          )}
        >
          {screenOrientation?.isLandScape && <ResultQrcodeComp />}
          {/* 制作证件照 */}
          {bgOptions.length > 0 &&
            sizeOptions.length > 0 &&
            curImg?.categoryInfo?.map(it => it?.name).includes('证件照') &&
            !curImg?.support3DModeling &&
            !isSplitPrinting && (
              <div
                className={classNames(
                  'flex justify-center',
                  screenOrientation.isLandScape && 'pt-16'
                )}
              >
                <Button
                  variant="minor"
                  className="w-[320px]"
                  size="lg"
                  disabled={resultImages.some(
                    it => it.status !== MirrorAiTaskStatus.SUCCESS
                  )}
                  onClick={() => setEditOpen(true)}
                >
                  <Photograph className="mr-2" size={40} />
                  <span>{t('制作证件照')}</span>
                </Button>
              </div>
            )}
          {/* 制作3d模型 */}
          {!!curImg?.support3DModeling && !isSplitPrinting && (
            <div
              className={classNames(
                'flex justify-center',
                screenOrientation.isLandScape && 'pt-16'
              )}
            >
              <Button
                variant="minor"
                className="w-[320px]"
                size="lg"
                disabled={resultImages.some(
                  it => it.status !== MirrorAiTaskStatus.SUCCESS
                )}
                onClick={() => setThreeDOpen(true)}
              >
                <Icon3d className="mr-2" size={40} />
                <span>3D 建模</span>
              </Button>
            </div>
          )}
          {hasPrinter ? (
            <>
              {!isSplitPrinting ? (
                <div
                  className={classNames(
                    'flex justify-center',
                    screenOrientation.isLandScape && 'pt-12'
                  )}
                >
                  <Button
                    size="lg"
                    className="w-[320px]"
                    disabled={resultImages.some(
                      it => it.status !== MirrorAiTaskStatus.SUCCESS
                    )}
                    loading={templateLoading}
                    onClick={() => toPrint()}
                  >
                    <MirrorPicture className="mr-2" size={40} />
                    <span>
                      {t(isPad() || isIPad() ? '保存并打印照片' : '打印照片')}
                    </span>
                  </Button>
                </div>
              ) : (
                <div
                  className={classNames(
                    'flex justify-center',
                    screenOrientation.isLandScape && 'pt-12'
                  )}
                >
                  <Button
                    className="w-[320px]"
                    size="lg"
                    onClick={() => {
                      setBackOpen(true)
                    }}
                  >
                    {t('返回首页')}
                  </Button>
                </div>
              )}
            </>
          ) : (
            <>
              {isPad() || isIPad() ? (
                <div
                  className={classNames(
                    'flex justify-center',
                    screenOrientation.isLandScape && 'pt-12'
                  )}
                >
                  <Button
                    size="lg"
                    className="w-[320px]"
                    disabled={resultImages.some(
                      it => it.status !== MirrorAiTaskStatus.SUCCESS
                    )}
                    onClick={() => {
                      downImage({
                        imageUrls: resultImages
                          .map(it => {
                            return it.editResultUrls?.[1]
                              ? [it.editResultUrls?.[1], it.resultUrl]
                              : it.resultUrl
                          })
                          .flat(),
                      })
                      toast({
                        description: t('保存成功'),
                      })
                    }}
                  >
                    <MirrorPicture className="mr-2" size={40} />
                    <span>{t('保存至本地')}</span>
                  </Button>
                </div>
              ) : (
                <div
                  className={classNames(
                    'flex justify-center',
                    screenOrientation.isLandScape && 'pt-12'
                  )}
                >
                  <Button
                    className="w-[320px]"
                    size="lg"
                    onClick={() => {
                      setBackOpen(true)
                    }}
                  >
                    {t('返回首页')}
                  </Button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
      <ErrorModal
        open={errorOpen}
        setOpen={setErrorOpen}
        errorCount={errorCount}
        onRetry={async () => {
          stopPollAiTaskStatus()
          await retryAiTask(taskBaseId)
          pollStatus()
        }}
      />
      {bgOptions.length > 0 && sizeOptions.length > 0 && editOpen && (
        <AvatarModal
          open={editOpen}
          onClose={() => {
            setEditOpen(false)
          }}
          onSuccess={() => {
            pollStatus()
          }}
          list={resultImages}
          bgOptions={bgOptions}
          sizeOptions={sizeOptions}
        />
      )}

      {threeDOpen && (
        <ThreeDModal
          open={threeDOpen}
          onClose={() => {
            setThreeDOpen(false)
          }}
          onSuccess={() => {
            pollStatus()
          }}
          list={resultImages}
        />
      )}
    </>
  )
}
