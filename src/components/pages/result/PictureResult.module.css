.photo {
  border-radius: 24px;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.photoHorizontal {
  border-radius: 24px;
  background: #fff;
  overflow: hidden;
}

.photoItem {
  padding: 24px;
  width: 656px;
  height: 960px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.photoItemHorizontal {
  padding: 24px;
  width: 960px;
  height: 656px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.photoTabWrap {
  width: 176px;
  height: 960px;
  background-color: var(--neutral-700);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.photoTab {
  width: 100%;
  background-image: linear-gradient(to right, #fff 50%, #f4f5f6 50%);
}

.photoTabWrapHorizontal {
  width: 960px;
  background-color: var(--neutral-700);
  display: flex;
  justify-content: center;
  align-items: center;
}

.photoTabHorizontal {
  background-image: linear-gradient(to bottom, #fff 50%, #f4f5f6 50%);
  display: flex;
  justify-content: center;
  align-items: center;
}

.tabItem {
  padding: 24px;
  height: 240px;
  background-color: var(--neutral-700);
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.tabItemHorizontal {
  padding: 24px;
  width: 240px;
  height: 160px;
  background-color: var(--neutral-700);
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}

.tabItemActive {
  background-color: #fff;
  border-radius: 24px;
}

.tabItemActivePrev {
  border-bottom-left-radius: 24px;
}

.tabItemHorizontal.tabItemActivePrev {
  border-top-right-radius: 24px;
}

.tabItemActiveNext {
  border-top-left-radius: 24px;
}

.tabItemHorizontal.tabItemActiveNext {
  border-bottom-right-radius: 24px;
}

.qrcode {
  margin-left: 200px;
}

.qrcodeItem {
  padding: 24px;
  border-radius: 16px;
  background-color: #fff;
}
.customQrcode {
  @apply ml-[200px] -mt-[150px];
  :global(.qrcode-wrap) {
    @apply p-3 scale-[0.55] origin-left;
  }
}

/* 临时 下载页面 */
.photoDownload {
  padding: 24px;
  width: 100%;
  height: auto;
  display: flex;
  justify-content: center;
  flex-direction: column;
  text-align: center;
}
.photoDownloadTabWrap {
  width: 100%;
  height: 100px;
  background-color: var(--neutral-700);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
}、
.photoDownloadTab {
  display: flex;
  align-items: center;
}
.photoDownloadTabItem{

}
.photoDownloadTabWrap > div > div{
  width: 88px;
  height: 88px;
  padding: 12px;
}

/* maze */
/* 横 */
.mazeImgListHorizontal{
  @apply flex justify-start items-center gap-10 py-5 px-20 my-10;
  max-width: 100vw;
  min-width: 80vw;
  overflow-x: auto;
}
/* 横 */
.mazeImgItemHorizontal{
  min-width: 320px;
  width: 22%;
}
/* 竖 */
.mazeImgList{
  @apply flex justify-start align-middle gap-10 py-10 px-10 ipad:py-5 ipad:gap-6;
  max-height: 80dvh;
  overflow-x: auto;
  width: 100vw;
}
/* 竖 */
.mazeImgItem{
  width: 44vw;
  max-width: 44vw;
  @media (max-width: 1024px) {
    width: 32vw;
    max-width: 32vw;
  }
}

/* 隐藏滚动条 */
.hideScrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hideScrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
