import { useAiTask } from '@/components/pages/photo/useAiTask'
import ResultQrcodeComp from './ResultQrcodeComp'
import styles from './VideoResult.module.css'
import { Button } from '@/components/ui/shad/button'
import { useSearchParams } from 'react-router-dom'
import { useEffect, useMemo, useRef, useState } from 'react'
import {
  AiTaskDetailType,
  MirrorAiTaskStatus,
  MultiScreenDisplayEnum,
  PrintTemplateSize,
  useCreateVideoPlayTaskMutation,
  useReserveVideoPlayTaskMutation,
} from '@/graphqls/types'
import { MyCheckbox } from '@/components/ui/MyCheckbox'
import { useAtom, useAtomValue } from 'jotai'
import {
  isSplitPrintingAtom,
  machineInfoAtom,
  multiScreenDisplayAtom,
  printerEnableAtom,
  screenOrientationAtom,
  isOfficialMerchantAtom,
} from '@/stores'
import { MyMirrorAiTask, PrinterStatus } from '@/stores/types'
import { MirrorPicture } from 'wujieai-react-icon'
import { MainImage } from './MainImage'
import { ErrorModal } from '@/components/pages/photo/LoadingModal'
import {
  ErrorModal as PrintErrorModal,
  LoadingModal as PrintLoadingModal,
  SuccessModal as PrintSuccessModal,
} from '@/components/pages/print/LoadingModal'
import { useTemplateSplicing } from '@/hooks/useTemplateSplicing'
import { useBridge } from '@/hooks/useBridge'
import { toAppNoCacheTag, toRotateImage } from '@/utils'
import { PRINTER_TYPE } from '@/configs'
import { useDebounce } from '@/hooks/useDebounce'
import classNames from 'classnames'
import { CDNImage } from '@/components/ui/CDNImage'
import { MainVideo } from './MainVideo'

const ClaimSteps = () => (
  <div>
    <div className="flex items-center justify-start">
      <span className="px-2 py-0.5 bg-neutral-50/[0.08] rounded-full text-neutral-300 text-sm mr-2">
        步骤一
      </span>
      <span className="text-[18px] leading-6 text-neutral-50">
        使用
        <span className="font-semibold mx-1">微信扫描右侧二维码</span>
        领取视频
      </span>
    </div>

    <div className="flex items-center justify-start mt-2">
      <span className="px-2 py-0.5 bg-neutral-50/[0.08] rounded-full text-neutral-300 text-sm mr-2">
        步骤二
      </span>
      <span className="text-[18px] leading-6 text-neutral-50">
        小程序
        <span className="font-semibold mx-1">首页-扫一扫</span>
        打印的图片查看 AR 视频
      </span>
    </div>
  </div>
)
export const VideoResult = ({
  setBackOpen,
}: {
  setBackOpen: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const isOfficialMerchant = useAtomValue(isOfficialMerchantAtom)
  // 是否可以投送
  const [canDrop, setCanDrop] = useState(true)
  // 是否选中投送
  const [droped, setDroped] = useState(true)
  const dropedRef = useRef(droped)
  /** 视频生成完成 */
  const generateVideoSuccess = useRef(false)
  /** 生成的换脸图 */
  const [generateFaceImage, setGenerateFaceImage] = useState<MyMirrorAiTask>()
  /** 生成的视频 */
  const [generateVideo, setGenerateVideo] = useState<MyMirrorAiTask>()
  const [isSplitPrinting] = useAtom(isSplitPrintingAtom)

  const [errorOpen, setErrorOpen] = useState(false)
  const [errorCount, setErrorCount] = useState(0)

  const [searchParams] = useSearchParams()

  const taskBaseId = Number(searchParams.get('taskBaseId'))

  const { pollAiTaskStatus, stopPollAiTaskStatus, retryAiTask } = useAiTask()

  const [createVideoPlayTask] = useCreateVideoPlayTaskMutation()
  const [reserveVideoPlayTask] = useReserveVideoPlayTaskMutation()

  const { imageSplicingResult } = useTemplateSplicing()
  const [multiScreenDisplay] = useAtom(multiScreenDisplayAtom)

  const [templateLoading, setTemplateLoading] = useState(false)

  const { printImage } = useBridge()
  /** 打印 loading 弹窗 */
  const [loadingOpen, setLoadingOpen] = useState(false)
  /** 打印错误弹窗 */
  const [printError, setPrintError] = useState(false)
  /** 打印完成 */
  const [printDone, setPrintDone] = useState(false)
  /** 打印错误失败重试次数 */
  const [retryCount, setRetryCount] = useState(0)
  const [printErrorImage, setPrintErrorImage] = useState<string>()

  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)

  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])

  const pollStatus = () => {
    pollAiTaskStatus({
      taskBaseId,
      onProgress({ taskList }) {
        // 过程中，仅进度条使用使用视频实际的进度
        const realPrecent = taskList?.find(it => {
          return it.detailType === AiTaskDetailType.VIDEO
        })?.generatingCompletePercent

        setGenerateFaceImage(
          taskList
            ?.filter(it => {
              return it.detailType === AiTaskDetailType.DRAW
            })
            ?.map(it => {
              return {
                ...it,
                generatingCompletePercent: realPrecent,
              }
            })?.[0]
        )
      },
      onFail() {
        setErrorOpen(true)
        setErrorCount(val => val + 1)
      },
      onSuccess({ taskList }) {
        setGenerateFaceImage(
          taskList?.find(it => it.detailType === AiTaskDetailType.DRAW)
        )
        setGenerateVideo(
          taskList?.find(it => it.detailType === AiTaskDetailType.VIDEO)
        )
        generateVideoSuccess.current = true
      },
    })
  }

  const toPrint = async (url: string) => {
    try {
      setLoadingOpen(true)
      printImage({
        content: [
          {
            imageUrl: screenOrientation.isPortrait ? url : toRotateImage(url),
            count: 1,
            type: PRINTER_TYPE[PrintTemplateSize.SIX_INCHES],
          },
        ],
        callback: async (cbRes: any) => {
          if (cbRes?.results?.some((it: any) => !it.success)) {
            setLoadingOpen(false)
            setPrintError(true)
            setPrintErrorImage(url)
          } else {
            setTimeout(() => {
              // 全部成功
              setLoadingOpen(false)
              setPrintDone(true)
            }, 5000)
          }
        },
      })
    } catch (error) {}
  }
  const getPrintBgUrl = () => {
    if (!isOfficialMerchant) {
      // 非官方无二维码
      return screenOrientation.isPortrait
        ? toAppNoCacheTag(
            'https://cdn.wujiebantu.com/upload/4BBC918DB0698285EB6610BCFA197125/1736214839421/FuvgjPPOnjkPZNEvSOQnAhSwEGm9.jpg'
          )
        : toAppNoCacheTag(
            'https://cdn.wujiebantu.com/upload/02C147D2F0ABECB908A13931C0CDE4FB/ai_init_image/1735520798895/FnH0P7cuPLLW95NVw4uBSgw1xMqI.png'
          )
    }
    if (import.meta.env.MODE === 'test') {
      return screenOrientation.isLandScape
        ? toAppNoCacheTag(
            'https://cdn.wujiebantu.com/upload/4BBC918DB0698285EB6610BCFA197125/1733991911340/FvTuvQiTdS0L0lRaj5yiiKau9hsV.jpg'
          )
        : toAppNoCacheTag(
            'https://cdn.wujiebantu.com/upload/4BBC918DB0698285EB6610BCFA197125/1736214525605/FsmbTcNsNiOWVDC-_4_THhNKeRBo.jpg'
          )
    }
    return screenOrientation.isLandScape
      ? toAppNoCacheTag(
          'https://cdn.wujiebantu.com/upload/4BBC918DB0698285EB6610BCFA197125/1733991987918/FjPnyqPCQVZFJqiI4SXIj7W6DnGZ.jpg'
        )
      : toAppNoCacheTag(
          'https://cdn.wujiebantu.com/upload/4BBC918DB0698285EB6610BCFA197125/1736214502014/FgJaH6HdFw9sp0PbXpDEWWq7yPoV.jpg'
        )
  }

  const toGenUrlAPrint = useDebounce(async () => {
    // 生成模板拼接图
    try {
      setTemplateLoading(true)
      const url = await imageSplicingResult({
        template: screenOrientation.isPortrait
          ? {
              templateWidth: 1200,
              templateHeight: 1800,
              bgUrl: getPrintBgUrl(),
              location: [
                {
                  x: 600,
                  y: -539,
                  width: 782,
                  height: 1390,
                },
                {
                  x: 600,
                  y: 900,
                  width: 782,
                  height: 1390,
                },
                {
                  x: 600,
                  y: 1800 + 539,
                  width: 782,
                  height: 1390,
                },
              ],
            }
          : {
              templateWidth: 1800,
              templateHeight: 1200,
              bgUrl: getPrintBgUrl(),
              location: [
                {
                  x: -539,
                  y: 600,
                  width: 1390,
                  height: 782,
                },
                {
                  x: 900,
                  y: 600,
                  width: 1390,
                  height: 782,
                },
                {
                  x: 1800 + 539,
                  y: 600,
                  width: 1390,
                  height: 782,
                },
              ],
            },
        imgUrls: [
          toAppNoCacheTag(generateFaceImage?.resultUrl!),
          toAppNoCacheTag(generateFaceImage?.resultUrl!),
          toAppNoCacheTag(generateFaceImage?.resultUrl!),
        ],
      })
      setTemplateLoading(false)
      toPrint(url!)
    } catch (error) {
      console.log(error)
    }
  }, [generateFaceImage])

  /** 解决在onSuccess中执行，闭包数据不更新 */
  useEffect(() => {
    if (
      !generateVideoSuccess.current ||
      multiScreenDisplay === MultiScreenDisplayEnum.SAME
    )
      return
    setCanDrop(false)
    if (dropedRef.current) {
      createVideoPlayTask({
        variables: {
          baseId: taskBaseId,
        },
      })
    }
  }, [
    generateVideoSuccess.current,
    dropedRef.current,
    taskBaseId,
    multiScreenDisplay,
  ])

  useEffect(() => {
    pollStatus()
    return () => {
      stopPollAiTaskStatus()
    }
  }, [])

  /** 离开页面上报未成功的投屏 */
  useEffect(() => {
    return () => {
      if (
        dropedRef.current &&
        !generateVideoSuccess.current &&
        multiScreenDisplay === MultiScreenDisplayEnum.DIFFERENT
      ) {
        reserveVideoPlayTask({
          variables: {
            baseId: taskBaseId,
          },
        })
      }
    }
  }, [])

  return (
    <>
      <div
        className={classNames(
          'flex items-center justify-center h-full',
          screenOrientation.isPortrait && 'flex-col'
        )}
      >
        {screenOrientation?.isPortrait && (
          <ResultQrcodeComp
            className="px-6 py-4"
            qrCodeClassName="!pr-0"
            qrCodeSize="sm"
            notice={
              <div className="mr-4" onClick={toGenUrlAPrint}>
                <div className="flex items-center mb-4">
                  <CDNImage
                    src="/images/common/wechat_round.png"
                    alt=""
                    className="w-[48px] h-[48px] mr-2"
                  />
                  <span>微信扫码领取视频</span>
                </div>
                {isOfficialMerchant && <ClaimSteps />}
              </div>
            }
          />
        )}
        {!!generateFaceImage?.id && (
          <div
            className={classNames(
              screenOrientation.isPortrait ? 'mt-[48px] ' : 'mr-[127px]'
            )}
          >
            <div
              className={classNames(
                'shadow-box-neutral bg-white rounded-3xl overflow-hidden p-6',
                screenOrientation.isPortrait
                  ? 'w-[626px] h-[1076px] mx-auto'
                  : 'w-[1008px] h-[588px]'
              )}
            >
              <div className="rounded-3xl w-full h-full overflow-hidden">
                <MainVideo
                  curImg={generateFaceImage}
                  curVideo={generateVideo}
                  imgClassName="object-cover"
                />
              </div>
            </div>
            <div className="mt-8 flex justify-between items-center px-10 w-[1008px]">
              {screenOrientation.isLandScape && (
                <>
                  {isOfficialMerchant && <ClaimSteps />}

                  {multiScreenDisplay === MultiScreenDisplayEnum.DIFFERENT && (
                    <div className="h-[54px] w-[1px] bg-neutral-950/20"></div>
                  )}
                </>
              )}
              {multiScreenDisplay === MultiScreenDisplayEnum.DIFFERENT && (
                <>
                  <div className="h-[54px] w-[1px] bg-neutral-950/20"></div>
                  <div>
                    <div className="text-neutral-50 font-bold text-lg flex items-center justify-start">
                      <MyCheckbox
                        checked={droped}
                        disabled={!canDrop}
                        onChange={val => {
                          setDroped(val)
                          dropedRef.current = val
                        }}
                      />
                      <div className="ml-4">
                        <div className="ml-2">将该视频投送到大屏展示</div>
                        <div className="text-sm font-normal text-neutral-400 text-center mt-1.5">
                          * 你的视频仅限用于本次投屏播放，不会用于其它用途
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        <div
          className={classNames(
            screenOrientation.isPortrait && 'flex justify-center gap-12'
          )}
        >
          {screenOrientation?.isLandScape && <ResultQrcodeComp />}
          <div className="mt-12">
            {hasPrinter && !!generateFaceImage?.id && !isSplitPrinting ? (
              <>
                <div className="flex justify-center pt-12">
                  <Button
                    size="lg"
                    className="w-[320px]"
                    disabled={
                      generateFaceImage?.status !== MirrorAiTaskStatus.SUCCESS
                    }
                    loading={templateLoading}
                    onClick={() => toGenUrlAPrint()}
                    ga-data="startPrint"
                  >
                    <MirrorPicture className="mr-2" size={40} />
                    <span>立即打印</span>
                  </Button>
                </div>
                <div className="text-sm font-normal text-neutral-400 text-center mt-6">
                  默认打印一张你的视频高光时刻封面图
                </div>
              </>
            ) : (
              <div className="flex justify-center pt-12">
                <Button
                  className="w-[320px]"
                  size="lg"
                  onClick={() => {
                    setBackOpen(true)
                  }}
                >
                  返回首页
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
      <ErrorModal
        open={errorOpen}
        setOpen={setErrorOpen}
        errorCount={errorCount}
        onRetry={async () => {
          stopPollAiTaskStatus()
          await retryAiTask(taskBaseId)
          pollStatus()
        }}
      />
      <PrintLoadingModal open={loadingOpen} setOpen={setLoadingOpen} />
      <PrintErrorModal
        open={printError}
        setOpen={setPrintError}
        onRetry={() => {
          toPrint(printErrorImage!)
          setRetryCount(count => count + 1)
        }}
        retryCount={retryCount}
      />
      <PrintSuccessModal open={printDone} />
    </>
  )
}
