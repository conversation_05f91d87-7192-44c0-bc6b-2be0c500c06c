import classnames from 'classnames'
import styles from './PictureResult.module.css'
import { Button } from '@/components/ui/shad/button'
import { useAtom } from 'jotai'
import {
  defaultPrintIndexAtom,
  resultImagesAtom,
  resultOrderAtom,
} from '@/stores'
import { useSearchParams } from 'react-router-dom'
import { MirrorPicture } from 'wujieai-react-icon'
import { useEffect, useState } from 'react'
import { useAiTask } from '@/components/pages/photo/useAiTask'
import { ErrorModal } from '@/components/pages/photo/LoadingModal'
import { Direction, MirrorAiTaskStatus } from '@/graphqls/types'
import { MainImage } from '@/components/pages/result/MainImage'
import { ImageDownloadNav } from '@/components/pages/result/ImageDownloadNav'
import { toast } from '@/components/ui/shad/use-toast'
import classNames from 'classnames'
import { useTranslation } from 'react-i18next'
import { useDevice } from '@/hooks/useDevice'
import { downloadRemoteFile } from '@/utils'

const PictureResult = () => {
  const [defaultPrintIndex, setDefaultPrintIndex] = useAtom(
    defaultPrintIndexAtom
  )
  const [resultImages, setResultImages] = useAtom(resultImagesAtom)
  const [, setResultOrder] = useAtom(resultOrderAtom)

  const { injectDeviceToken } = useDevice()
  const { t } = useTranslation()

  const [errorOpen, setErrorOpen] = useState(false)
  const [errorCount, setErrorCount] = useState(0)

  const [searchParams] = useSearchParams()

  const taskBaseId = Number(searchParams.get('taskBaseId'))

  const { pollAiTaskStatus, retryAiTask, stopPollAiTaskStatus } = useAiTask()

  useEffect(() => {
    // TODO: 注入token，后期替换为 maze 接口
    injectDeviceToken()
  }, [])

  const pollStatus = () => {
    pollAiTaskStatus({
      taskBaseId,
      onProgress({ taskList }) {
        setResultImages(taskList)
        // if (!isSplitPrinting && isOfficialMerchant) {
        //   imageSplicing(taskList)
        // }
      },
      onFail() {
        setErrorOpen(true)
        setErrorCount(val => val + 1)
      },
      onSuccess({ order, taskList }) {
        setResultImages(taskList)
        setResultOrder(order)
        // if (!isSplitPrinting && isOfficialMerchant) {
        //   imageSplicing(taskList)
        // }
      },
    })
  }

  useEffect(() => {
    pollStatus()

    return () => {
      stopPollAiTaskStatus()
    }
  }, [])
  // 当前聚焦的图片
  const curImg = resultImages?.[defaultPrintIndex]

  return (
    <>
      <div
        className={classNames(
          'flex items-center flex-col justify-center h-full pl-4 pr-4'
        )}
      >
        <div
          className={classnames(styles.photo, 'shadow-box-neutral flex-col')}
          style={{ overflow: 'visible' }}
        >
          <div className={classnames([styles.photoDownload])}>
            <MainImage curImg={curImg} />
          </div>
          {resultImages.length > 1 && (
            <ImageDownloadNav
              resultImages={resultImages}
              direction={Direction.VERTICAL}
              defaultPrintIndex={defaultPrintIndex}
              setDefaultPrintIndex={setDefaultPrintIndex}
              styles={styles}
              className={styles.photoDownloadTabWrap}
            />
          )}
        </div>
        {/* 右侧功能栏 */}
        <div className={classNames('flex justify-center mt-8')}>
          <div className={classNames('flex justify-center')}>
            <Button
              size="lg"
              className="w-[320px]"
              disabled={resultImages.some(
                it => it.status !== MirrorAiTaskStatus.SUCCESS
              )}
              onClick={() => {
                const urls = resultImages
                  .map(it => {
                    return it.editResultUrls?.[1]
                      ? [it.editResultUrls?.[1], it.resultUrl]
                      : it.resultUrl
                  })
                  .flat()
                console.log('first', urls)
                downloadRemoteFile(curImg?.resultUrl as string)
                toast({
                  description: t('保存成功'),
                })
              }}
            >
              <MirrorPicture className="mr-2" size={40} />
              <span>{t('保存至本地')}</span>
            </Button>
          </div>
        </div>
      </div>
      <ErrorModal
        open={errorOpen}
        setOpen={setErrorOpen}
        errorCount={errorCount}
        onRetry={async () => {
          stopPollAiTaskStatus()
          await retryAiTask(taskBaseId)
          pollStatus()
        }}
      />
    </>
  )
}

export default PictureResult
