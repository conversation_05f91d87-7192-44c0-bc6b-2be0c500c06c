import { useAtom } from 'jotai'
import {
  mazeResultImagesAtom,
  resultOrder<PERSON>tom,
  screenOrientationAtom,
} from '@/stores'
import { MirrorAiTaskStatus } from '@/graphqls/types'
import { useState, useMemo } from 'react'
import { useAiTask } from '@/components/pages/photo/useAiTask'
import { ErrorModal } from '@/components/pages/photo/LoadingModal'
import { MainImage } from '@/components/pages/result/MainImage'
import { useVisibilityAwarePoll } from '@/hooks/useVisibilityAwarePoll'
import { AiTaskDetailType } from '@/graphqls/types'

export const MazePictureResultItem = ({
  taskBaseId,
}: {
  taskBaseId: number
}) => {
  const [curSelectedIndex, setCurSelectedIndex] = useState<null | number>(null)
  const [resultImagesMap, setResultImagesMap] = useAtom(mazeResultImagesAtom)
  const [, setResultOrder] = useAtom(resultOrderAtom)
  const [screenOrientation] = useAtom(screenOrientationAtom)

  // Get the current task's images from the map
  const resultImages = useMemo(() => {
    return resultImagesMap[taskBaseId] || []
  }, [resultImagesMap, taskBaseId])

  const [errorOpen, setErrorOpen] = useState(false)
  const [errorCount, setErrorCount] = useState(0)

  const { pollAiTaskStatus, retryAiTask, stopPollAiTaskStatus } = useAiTask()
  const pollStatus = () => {
    pollAiTaskStatus({
      taskBaseId,
      onProgress({ taskList }) {
        // 过程中，仅进度条使用使用视频实际的进度
        const videoPercent = taskList?.find(it => {
          return it.detailType === AiTaskDetailType.VIDEO
        })?.generatingCompletePercent
        const imagePercent = taskList?.find(it => {
          return it.detailType === AiTaskDetailType.DRAW
        })?.generatingCompletePercent
        const realPercent = videoPercent ?? imagePercent

        setResultImagesMap(prev => ({
          ...prev,
          [taskBaseId]: taskList?.map(it => {
            if (it.detailType === AiTaskDetailType.DRAW) {
              return {
                ...it,
                generatingCompletePercent: realPercent,
              }
            }
            return it
          }),
        }))
      },
      onFail() {
        pollControl.resetPollStatus()
        setErrorOpen(true)
        setErrorCount(val => val + 1)
      },
      onSuccess({ order, taskList }) {
        pollControl.resetPollStatus()
        setResultImagesMap(prev => ({
          ...prev,
          [taskBaseId]: taskList,
        }))
        setResultOrder(order)
      },
    })
  }

  // 使用页面可见性感知的轮询hook
  const pollControl = useVisibilityAwarePoll(pollStatus, stopPollAiTaskStatus, [
    taskBaseId,
  ])

  console.log('result', resultImages, screenOrientation)
  const videos = useMemo(() => {
    return resultImages?.filter(it => it.detailType === AiTaskDetailType.VIDEO)
  }, [resultImages])

  return (
    <div
      className="overflow-hidden"
      style={{
        aspectRatio: '0.66',
      }}
    >
      {resultImages
        ?.filter(it => it.detailType === AiTaskDetailType.DRAW)
        ?.map((item, index) => (
          <div
            className="w-full h-full"
            key={index}
            onClick={() =>
              item.status === MirrorAiTaskStatus.SUCCESS
                ? setCurSelectedIndex(index)
                : () => void 0
            }
          >
            <MainImage
              curVideo={videos?.[index]}
              curImg={item}
              imgClassName="object-cover"
              className="h-full object-cover"
            />
          </div>
        ))}
      <ErrorModal
        open={errorOpen}
        setOpen={setErrorOpen}
        errorCount={errorCount}
        onRetry={async () => {
          pollControl.stopPoll()
          await retryAiTask(taskBaseId)
          pollControl.startPoll()
        }}
      />
    </div>
  )
}
