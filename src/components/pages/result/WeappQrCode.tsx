import { <PERSON><PERSON><PERSON><PERSON>and<PERSON>, useEffect, useState } from 'react'
import { defaultPrintIndexAtom, drawPayOrderAtom, taskTypeAtom } from '@/stores'
import IconLinearLoading from '@/components/ui/icons/LinearLoading'
import { useAtom } from 'jotai'
import {
  AiTaskType,
  MirrorWeappCodeUrlDocument,
  MirrorWeappCodeUrlQuery,
  MirrorWeappCodeUrlQueryVariables,
} from '@/graphqls/types'
import classNames from 'classnames'

import { useImperativeQuery } from '@/hooks/useImperativeQuery'
import { IconRefresh } from 'wujieai-react-icon'
import { useSearchParams } from 'react-router-dom'
import { toAppNoCacheTag } from '@/utils'

/** 二维码状态 */
enum QrCodeStatus {
  /** 加载中 */
  'PENDING' = 'PENDING',
  /** 加载成功 */
  'SUCCESS' = 'SUCCESS',
  /** 加载失败 */
  'ERROR' = 'ERROR',
}

/** 小程序二维码 */
export const WeappQrCode: React.FC<{ size?: string }> = ({ size }) => {
  const [drawOrderInfo] = useAtom(drawPayOrderAtom)
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [qrStatus, setQrStatus] = useState<QrCodeStatus>(QrCodeStatus.PENDING)
  const [defaultPrintIndex] = useAtom(defaultPrintIndexAtom)

  const [searchParams] = useSearchParams()
  const taskBaseId = Number(searchParams.get('taskBaseId'))

  const [taskType] = useAtom(taskTypeAtom)

  const getAccountQrCodeAction = useImperativeQuery<
    MirrorWeappCodeUrlQuery,
    MirrorWeappCodeUrlQueryVariables
  >(MirrorWeappCodeUrlDocument)

  const getQrCode = async () => {
    try {
      setQrStatus(QrCodeStatus.PENDING)
      const res = await getAccountQrCodeAction({
        input: {
          envVersion: import.meta.env.MODE === 'test' ? 'trial' : 'release',
          page:
            taskType === AiTaskType.DRAW
              ? `pages/mine/albumClaim/index`
              : `pages/mine/videoDetail/index`,
          scene: `c=${drawOrderInfo?.order?.id}&i=${defaultPrintIndex}&t=${taskBaseId}`,
          width: 100,
          checkPath: false,
          isHyaline: false,
        },
      })

      const url = res?.data?.weappQuery?.mirrorWeappCodeUrl

      url && setQrCodeUrl(url)
      setQrStatus(QrCodeStatus.SUCCESS)
    } catch (error) {
      setQrStatus(QrCodeStatus.ERROR)
      console.log('qrcode-error', error)
    }
  }

  useEffect(() => {
    getQrCode()
  }, [])

  return (
    <div
      className={classNames(
        'qrcode-wrap flex rounded-[16px] bg-neutral-900 text-center relative    text-neutral-50 text-lg font-bold',
        'shadow-box-neutral',
        size === 'sm' ? 'p-4' : 'p-6'
      )}
    >
      <img
        src={toAppNoCacheTag(qrCodeUrl)}
        className={classNames(
          size === 'sm' ? 'w-[192px] h-[192px]' : 'w-[348px] h-[348px]'
        )}
      />
      {qrStatus === QrCodeStatus.PENDING && (
        <StatusModule onClick={getQrCode}>
          <div className="w-[144px] h-[144px]">
            <IconLinearLoading className={'animate-spin mb-4'} />
            <div>二维码加载中</div>
            <div>请稍后</div>
          </div>
        </StatusModule>
      )}
      {qrStatus === QrCodeStatus.ERROR && (
        <StatusModule onClick={getQrCode}>
          <div className="w-[64px] h-[64px] bg-neutral-600 rounded-full flex justify-center items-center mb-4">
            <IconRefresh className="text-neutral-50" />
          </div>
          <div>二维码加载失败</div>
          <div>请点击重试</div>
        </StatusModule>
      )}
    </div>
  )
}

const StatusModule = ({
  onClick,
  style,
  className,
  children,
}: {
  children: React.ReactNode
  onClick?: MouseEventHandler
  style?: React.CSSProperties
  className?: string
}) => {
  return (
    <div
      className={classNames(
        className,
        'rounded-[16px] absolute top-0 left-0 z-100 bg-neutral-900 w-full h-full flex justify-center items-center flex-col '
      )}
      onClick={onClick}
      style={style}
    >
      {children}
    </div>
  )
}
