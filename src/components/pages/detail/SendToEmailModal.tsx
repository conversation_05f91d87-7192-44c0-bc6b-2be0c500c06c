import { useState } from 'react'
import { MyModal } from '@/components/ui/MyModal'
import { Input } from '@/components/ui/shad/input'
import { useTranslation } from 'react-i18next'
import { screenOrientationAtom } from '@/stores'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { toast } from '@/components/ui/shad/use-toast'
import { useAtomValue } from 'jotai'

export const SendToEmailModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  imgId: { material_id: number; image_id: number }[] | null
}> = ({ open, setOpen, imgId }) => {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState<boolean>(false)
  const { t } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value)
  }

  const handleSend = async () => {
    const isEmail = /^\w+(?:[-.]\w+)*@\w+(?:[-.]\w+)*\.\w+(?:[-.]\w+)*$/
    if (!email || !isEmail.test(email)) {
      toast({
        description: t('请输入正确的 Email 地址'),
      })
      return
    }
    setLoading(true)
    const res = await _ajax.post(_api.send_image_email, {
      email: email?.trim(),
      material_ids: imgId?.map(it => it.material_id),
    })
    if (res.data.code === 200) {
      toast({
        description: t('发送成功'),
      })
    }
    handleClose()
    setLoading(false)
  }

  const handleClose = () => {
    setEmail('')
    setOpen?.(false)
  }

  return (
    <MyModal
      title=""
      open={open}
      width={screenOrientation.isLandScape ? 800 : '80vw'}
      content={
        <div className="text-[2rem] text-center font-semibold leading-[2.625rem] flex-1 px-8 pt-6">
          <Input
            autoFocus
            value={email}
            onChange={handleEmailChange}
            className="border-3 border-gray-500 h-20 px-8 text-[32px] w-full rounded-full maze-bg-primary phone:text-[14px] phone:border-[1px]"
          />
          <p className="py-12">{t('输入 Email 接收图片')}</p>
        </div>
      }
      onOk={handleSend}
      okText={t('确定')}
      contentClassName="p-0 w-full"
      okLoading={loading}
      onCancel={handleClose}
    />
  )
}
