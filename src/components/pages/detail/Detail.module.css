@import 'tailwindcss' reference;

.toolbar {
  @apply flex justify-center items-center gap-12;

  @media (min-width: 768px) and (max-width: 1024px) {
    @apply gap-16;
  }

  li {
    @apply relative border-2 rounded-full p-7 cursor-pointer w-36 h-36 flex justify-center items-center;

    @media (min-width: 320px) and (max-width: 475px) {
      @apply border-[1px];
    }

    @media (min-width: 768px) and (max-width: 1024px) {
      @apply w-[7rem] h-[7rem];
    }
  }
}
.toolbarHorizontal {
  li {
    @apply w-32 h-32;
  }
}
/* 竖 */
.detailItem {
  @apply w-auto h-auto max-w-[72%];

  @media (min-width: 768px) and (max-width: 1024px) {
    @apply max-w-[56%];
  }
}
/* 横 */
.detailItemHorizontal {
  @apply w-auto h-auto max-w-[22%];
}
