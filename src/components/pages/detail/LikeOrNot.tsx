import { useState } from 'react'
import { publicPreLoadSourceObj } from '@/configs/source'
import { Button } from '@/components/ui/shad/button'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { SvgIcon } from '@/components/ui/SvgIcon'

export const LikeOrNot: React.FC<{
  imgId: number | null
  after: () => void
}> = ({ imgId, after }) => {
  const [loading, setLoading] = useState<'up' | 'down' | null>(null)
  const handleRate = async (thumb: 'up' | 'down') => {
    setLoading(thumb)
    after?.()
    const data = {
      material_id: imgId,
      thumb,
    }
    await _ajax.post(_api.thumb_image_mirror, data)
    setLoading(null)
  }

  return (
    <div className="flex gap-6 px-3 justify-end">
      <Button
        className="w-[7.5rem] h-[7.5rem] rounded-full px-0 border-3 border-white hover:border-white cursor-pointer active:border-white maze-bg-gradient-btn"
        size="lg"
        variant="outline"
        onClick={() => handleRate('up')}
        // loading={loading === 'up'}
      >
        <SvgIcon
          src={publicPreLoadSourceObj.like}
          alt="点赞"
          svgClassName="w-14 h-14"
        />
      </Button>
      <Button
        className="w-[7.5rem] h-[7.5rem] rounded-full px-0 border-3 border-white hover:border-white cursor-pointer active:border-white backdrop-blur-[5px] bg-[#989898] bg-opacity-35"
        size="lg"
        variant="outline"
        onClick={() => handleRate('down')}
        // loading={loading === 'down'}
      >
        <SvgIcon
          src={publicPreLoadSourceObj.dislike}
          alt="点踩"
          svgClassName="w-14 h-14"
        />
      </Button>
    </div>
  )
}
