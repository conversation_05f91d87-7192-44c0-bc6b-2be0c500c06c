import { publicPreLoadSourceObj } from '@/configs/source'
import { SvgIcon } from '@/components/ui/SvgIcon'

export const ModalCloseBtn: React.FC<{
  handleClose: () => void
}> = ({ handleClose }) => {
  return (
    <div className="text-center text-neutral-50 font-bold text-[24px] leading-[32px] pt-6 absolute -bottom-28">
      <a className="inline-block" onClick={handleClose}>
        <SvgIcon
          src={publicPreLoadSourceObj.close}
          alt="关闭"
          className="w-16 h-16 rounded-full cursor-pointer"
          onClick={handleClose}
        />
      </a>
    </div>
  )
}
