import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { copy } from '@/utils'
import { screenOrientationAtom } from '@/stores'
import { useAtomValue } from 'jotai'
import ImgTwitter from '/images/common/twitter.png'
import ImgFacebook from '/images/common/facebook.png'
import ImgLink from '/images/common/link.png'

const tabTool = [
  {
    url: ImgTwitter,
    title: 'x',
  },
  {
    url: ImgFacebook,
    title: 'Facebook',
  },
  {
    url: ImgLink,
    title: 'Link',
  },
]
export const ShareModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  mazeImgUrl: string
}> = ({ open, setOpen, mazeImgUrl }) => {
  const { t, i18n } = useTranslation()
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const onCloseQuestion = () => {
    setOpen?.(false)
  }
  const handleClick = (op: string) => {
    const title =
      i18n.language === 'en-US'
        ? `Just got my AI-powered portrait from Maze Mirror! What do you think? 🤩✨${mazeImgUrl}`
        : `体验了一把 Maze Mirror 的AI写真，效果太惊喜了！你打几分？💯📷 ${mazeImgUrl}`
    switch (op) {
      case 'x':
        const twitterScheme = `twitter://post?message=${encodeURIComponent(title)} ${encodeURIComponent(window.location.href)}`
        const twitterFallbackUrl = `https://twitter.com/intent/tweet/?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(title)}`
        shareToApp({
          schema: twitterScheme,
          fallbackUrl: twitterFallbackUrl,
        })
        break
      case 'Facebook':
        const facebookSchema = `fb://facewebmodal/f?href=https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}}`
        const facebookFallbackUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}&t=${encodeURIComponent(title)}`
        shareToApp({
          schema: facebookSchema,
          fallbackUrl: facebookFallbackUrl,
        })
        break
      case 'Link':
        copy(window.location.href)
        break
      default:
    }
  }
  const shareToApp = ({
    schema,
    fallbackUrl,
  }: {
    schema: string
    fallbackUrl: string
  }) => {
    const iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    iframe.src = schema
    document.body.appendChild(iframe)

    // 设置超时 fallback 到网页
    setTimeout(() => {
      window.location.href = fallbackUrl
      document.body.removeChild(iframe)
    }, 1500)
  }

  return (
    <MyModal
      open={open}
      width={screenOrientation.isLandScape ? 600 : '60vw'}
      content={
        <ul className="flex items-center justify-between py-[15px] w-full gap-6">
          {tabTool.map(item => (
            <li
              className="flex-1 flex flex-col justify-center items-center"
              onClick={() => handleClick(item.title)}
            >
              <img width={42} src={item.url} alt=""></img>
              <span className="mt-6 text-[2rem]">{item.title}</span>
            </li>
          ))}
        </ul>
      }
      onOk={onCloseQuestion}
      showOkButton={false}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => setOpen?.(false)}
    />
  )
}
