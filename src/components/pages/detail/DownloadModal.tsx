import { MyModal } from '@/components/ui/MyModal'
import { useTranslation } from 'react-i18next'
import { DownloadQrCode } from '../result/DownloadQrCode'

export const DownloadModal: React.FC<{
  open: boolean
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>
  mazeImgUrl: string
  tipsText?: string
}> = ({ open, setOpen, mazeImgUrl, tipsText }) => {
  const { t } = useTranslation()
  const onCloseQuestion = () => {
    setOpen?.(false)
  }

  return (
    <MyModal
      open={open}
      width={600}
      content={
        <div className="flex-1 pt-6">
          <DownloadQrCode url={mazeImgUrl} />
          <p className="pt-12 pb-2 text-[2rem] text-center font-semibold leading-[140%]">
            {t(tipsText || '扫描二维码下载图片')}
          </p>
        </div>
      }
      onOk={onCloseQuestion}
      okText={t('完成')}
      showOkButton={false}
      showCancelButton={false}
      contentClassName="p-0 w-full"
      onCancel={() => setOpen?.(false)}
    />
  )
}
