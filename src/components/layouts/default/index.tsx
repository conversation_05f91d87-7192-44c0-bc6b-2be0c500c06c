import React, { useEffect, useMemo } from 'react'
import classNames from 'classnames'
import { Outlet } from 'react-router-dom'
import PreloadResource from './PreloadResource'
import styles from './index.module.css'
import { GlobalTimer } from '../../business/GlobalTimer'
import { AnalyticsScript } from '@/components/business/AnalyticsScript'
import { useThemeConfig } from '@/hooks/useThemeConfig'
import { isIphone } from '@/utils'
import Cookies from 'js-cookie'
import { VideoPlayerModal } from '@/components/pages/common/VideoPlayerModal'
import { DynamicBackground } from '@/components/ui/DynamicBackground'

export const Layout: React.FC = () => {
  const { themeConfig, injectGlobalStyles, removeGlobalStyles } =
    useThemeConfig()

  useEffect(() => {
    injectGlobalStyles(themeConfig)
    return () => {
      removeGlobalStyles()
    }
  }, [themeConfig])

  const isIphoneHasBar = useMemo(() => {
    if (isIphone()) {
      return Cookies.get('statusHeight')
    }
  }, [isIphone()])

  return (
    <>
      <PreloadResource>
        {/* 根据状态控制是否显示全局动态背景 */}
        <DynamicBackground>
          <div
            className={classNames(
              styles.container,
              'w-[100vw] h-[100vh] phone:w-[100dvw] phone:h-[100dvh]',
              isIphoneHasBar ? 'pt-[8dvh] pb-[3.25dvh]' : ''
            )}
          >
            <Outlet />
          </div>
        </DynamicBackground>
        <GlobalTimer />
        <VideoPlayerModal />
      </PreloadResource>
      <AnalyticsScript />
    </>
  )
}
