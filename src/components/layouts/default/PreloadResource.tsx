import React, { useEffect } from 'react'
import { usePreloadResource } from '@/hooks/usePreloadResource'
import SourceLoadStatus from '@/components/business/SourceLoadStatus'
import { useAtomValue } from 'jotai'
import { scaleAtom } from '@/stores'
import { getScaleStyle } from '@/utils'
// import Cookie from 'js-cookie'
// import { DEVICE_TOKEN } from '@/configs'
const PreloadResource: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  const { fetchResources, errorText, loadStatus, loadedRef } =
    usePreloadResource()
  const scale = useAtomValue(scaleAtom)
  // const loadStatus: 'undistributed' | 'success' = 'undistributed'

  useEffect(() => {
    fetchResources(window.innerWidth > 768)

    setTimeout(
      () => {
        // 未初始化完成, 定时刷新页面
        if (!loadedRef.current) {
          window.location.reload()
        }
      },
      8 * 60 * 1000
    )
  }, [])

  // const deviceToken = Cookie.get(DEVICE_TOKEN)
  // const iosToken =
  //   'eyJhbGciOiJIUzI1NiJ9.eyJkZXZpY2VJZCI6MTU0MiwiZGV2aWNlU2Vzc2lvbklkIjoxMTQ1LCJtZXJjaGFudE5vIjoiMDIzNDI2NiIsImRldmljZVR5cGVDb2RlIjoyLCJpYXQiOjE3NDQyNTUyMTQsInN1YiI6IjE1NDIifQ.ScI07RdFUx4uSnc1PtDr_j3HZgfl3JGefP3_4YwWe7A'

  // if (deviceToken && deviceToken === iosToken) {
  //   console.log('deviceToken', deviceToken)
  //   return (
  //     <SourceLoadStatus
  //       loadStatus={'fail'}
  //       errorText={'Loading failure, please exit the APP and open it again'}
  //       style={{
  //         ...getScaleStyle({
  //           transform: `scale(${scale})`,
  //         }),
  //       }}
  //       isReloadBtn={false}
  //     />
  //   )
  // }

  if (loadStatus !== 'success')
    return (
      <SourceLoadStatus
        loadStatus={loadStatus}
        errorText={errorText}
        style={{
          ...getScaleStyle({
            transform: `scale(${scale})`,
          }),
        }}
      />
    )

  return <>{children}</>
}
export default PreloadResource
