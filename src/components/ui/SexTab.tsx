import classnames from 'classnames'
import styles from './SexTab.module.css'
import { MirrorSexEnum } from '@/graphqls/types'
import { publicPreLoadSourceObj } from '@/configs/source'

function SexTab({
  items,
  activeKey,
  setActiveKey,
  className,
}: {
  items: { label: string; value: any }[]
  activeKey: any
  setActiveKey: (val: any) => void
  className?: string
}) {
  return (
    <div className={classnames(styles.tabs, className)}>
      {items.map(it => (
        <div
          className={classnames(styles.tab, {
            [styles.tabActive]: it.value === activeKey,
            [styles.male]: it.value === MirrorSexEnum.MALE,
            [styles.female]: it.value === MirrorSexEnum.FEMALE,
          })}
          key={it.value}
          onClick={() => setActiveKey(it.value)}
        >
          <img
            className="w-16 h-16"
            src={
              it.value === MirrorSexEnum.FEMALE
                ? publicPreLoadSourceObj.female
                : publicPreLoadSourceObj.male
            }
          />
        </div>
      ))}
    </div>
  )
}

export default SexTab
