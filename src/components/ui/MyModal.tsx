import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogFooter,
} from '@/components/ui/shad/alert-dialog'
import { Button } from './shad/button'
import { cn } from '@/utils/shad'
import { useAtomValue } from 'jotai'
import { scaleAtom } from '@/stores'
import { getScaleStyle } from '@/utils'
import { SvgIcon } from './SvgIcon'

export const MyModal: React.FC<{
  open: boolean
  title?: string
  content?: React.ReactNode
  cancelText?: string
  showCancelButton?: boolean
  okLoading?: boolean
  okText?: string
  showOkButton?: boolean
  className?: string
  width?: number | string
  footer?: React.ReactNode | null
  footerExtra?: React.ReactNode
  onCancel?: () => void
  onOk?: () => void
  contentClassName?: string
}> = ({
  open,
  content,
  width = 632,
  okText = '确定',
  okLoading = false,
  showOkButton = true,
  className,
  footer,
  onCancel,
  onOk,
  footerExtra,
  contentClassName,
}) => {
  const scale = useAtomValue(scaleAtom)

  return (
    <AlertDialog open={open}>
      <AlertDialogContent
        style={{
          width: width,
          maxWidth: width,
          ...getScaleStyle({
            transform: `scale(${scale})  translate(-50%,-50%)`,
            transformOrigin: '0 0',
          }),
          background: 'var(--primary-bg)',
        }}
        className={className}
      >
        {/** 不能用AlertDialogDescription，因为AlertDialogDescription是一个p标签,p标签里不能嵌套div */}
        {content && (
          <div
            className={cn(
              'flex p-4 justify-center items-center text-lg maze-primary-text',
              contentClassName
            )}
          >
            {content}
          </div>
        )}

        {footer !== null && (
          <AlertDialogFooter style={{ margin: '0 auto' }}>
            {/* {showCancelButton && (
              <Button
                className="w-[256px] mr-6"
                size="lg"
                variant="outline"
                onClick={onCancel}
              >
                {cancelText}
              </Button>
            )} */}
            {showOkButton && (
              <Button
                className="w-[16rem]"
                size="lg"
                onClick={onOk}
                loading={okLoading}
                variant="outline"
              >
                {okText}
              </Button>
            )}
          </AlertDialogFooter>
        )}
        {footerExtra}
        {
          <div className="text-center text-neutral-50 font-bold text-[24px] leading-[32px] mt-6 absolute -bottom-28 w-full">
            <a className="inline-block" onClick={onCancel}>
              <SvgIcon
                src="/images/icons/close.svg"
                alt="关闭"
                className="w-16 h-16 rounded-full cursor-pointer"
                onClick={onCancel}
              />
            </a>
          </div>
        }
      </AlertDialogContent>
    </AlertDialog>
  )
}
