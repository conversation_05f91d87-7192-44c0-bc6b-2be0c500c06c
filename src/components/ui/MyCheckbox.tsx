import React from 'react'
import classNames from 'classnames'
import { Check } from 'wujieai-react-icon'

export const MyCheckbox: React.FC<{
  checked?: boolean
  disabled?: boolean
  onChange?: (checked: boolean) => void
  className?: string
}> = ({ checked, disabled, onChange, className }) => {
  return (
    <div
      onClick={() => {
        if (!disabled) {
          onChange?.(!checked)
        }
      }}
      className={classNames(
        className,
        'w-10 h-10 flex items-center justify-center border-4 border-neutral-900 rounded-full',
        {
          'opacity-50': disabled,
        }
      )}
      style={{
        background: checked
          ? `linear-gradient(321deg, #6964DE -10.33%, #FCA6E9 100%)`
          : '#D9D9D9',
      }}
    >
      {checked && <Check size={34} color="#fff" />}
    </div>
  )
}
