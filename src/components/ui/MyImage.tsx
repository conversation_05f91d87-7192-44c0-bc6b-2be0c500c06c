import React, { useMemo } from 'react'
import { cn } from '@/utils/shad'
import { toAppNoCacheTag } from '@/utils'
/**
 * tag说明
 * v400 表示就改图片质量为75且宽度为400
 * v800 表示就改图片质量为75且宽度为800
 * v1200  表示就改图片质量为75且宽度为1200
 * **/

export const MyImage: React.FC<{
  src: string | null | undefined
  className?: string
  imgClassName?: string
  style?: React.CSSProperties
  quality?: number
  alt?: string
  tag?: 'v400' | 'v600' | 'v800' | 'v1200'
  roate?: boolean
  /** app资源缓存 */
  isAppCache?: boolean
  onClick?: () => void
}> = ({
  src,
  className,
  imgClassName,
  quality = 75,
  style,
  alt = '',
  onClick,
  tag,
  isAppCache = true,
  roate = false,
}) => {
  const tagSrc = useMemo(() => {
    if (!tag) return src!
    if (roate) {
      return tag
        ? `${src}?imageMogr2/rotate/270/thumbnail/${tag.replace('v', '')}x`
        : `${src}?imageMogr2/rotate/270`
    }
    return tag
      ? `${src}?imageView2/2/w/${tag.replace('v', '')}/q/${quality}`
      : `${src}?imageView2/2/q/${quality}`
  }, [src, tag, quality, roate])

  return (
    <div
      className={cn(
        'w-full h-full flex items-center justify-center overflow-hidden',
        className
      )}
      style={{
        ...style,
      }}
      onClick={onClick}
    >
      <img
        src={(!isAppCache ? toAppNoCacheTag(tagSrc) : tagSrc) || ''}
        className={cn(
          'object-contain min-w-full min-h-full max-w-full max-h-full',
          imgClassName
        )}
        alt={alt}
      />
    </div>
  )
}
