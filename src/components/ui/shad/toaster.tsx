import {
  Toast,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/shad/toast'
import { useToast } from '@/components/ui/shad/use-toast'

export function Toaster({ ...props }) {
  const { toasts } = useToast()

  return (
    <ToastProvider {...props}>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
          </Toast>
        )
      })}
      <ToastViewport {...props} />
    </ToastProvider>
  )
}
