import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/utils/shad'
import { MirrorLoading } from 'wujieai-react-icon'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-lg font-bold shadow-sm ring-offset-background transition-colors disabled:pointer-events-none disabled:opacity-50 active:brightness-90',
  {
    variants: {
      variant: {
        default: 'text-white shadow-lg',
        outline: 'border border-2 bg-transparent text-white',
        minor:
          'border border-white bg-white bg-opacity-56 text-purple-500 shadow-lg',
      },
      size: {
        default: 'h-12 px-12 py-6',
        md: 'h-16 px-8 py-4 ',
        lg: 'h-20 px-12 py-6 ',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)
type Size = 'default' | 'md' | 'lg'
const loadingSize: Record<Size, string> = {
  default: 'h-6 w-6',
  md: 'h-8 w-8 ',
  lg: 'h-10 w-10 ',
}

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant, size, asChild = false, loading = false, ...props },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button'
    // loading按钮禁用的样式
    if (loading) {
      props.disabled = true
    }

    // 为不同变体添加样式
    const getButtonStyle = () => {
      switch (variant) {
        case 'default':
          return {
            background:
              'linear-gradient(321deg, #6964de -10.33%, #fca6e9 100%)',
            color: 'var(--neutral-900)',
            boxShadow:
              '0px 76px 52px rgba(139, 92, 246, 0.2), 0px 20px 28px rgba(139, 92, 246, 0.36)',
            ...props.style,
          }
        case 'outline':
          return {
            borderColor: 'var(--primary-text)',
            color: 'var(--primary-text)',
            ...props.style,
          }
        case 'minor':
          return {
            boxShadow:
              '0px 76px 52px rgba(9, 9, 9, 0.06), 0px 20px 28px rgba(0, 0, 0, 0.12)',
            color: 'var(--primary)',
            ...props.style,
          }
        default:
          return props.style
      }
    }

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        style={getButtonStyle()}
        {...props}
      >
        {props.children}
        {loading && (
          <MirrorLoading
            className={cn(
              loadingSize[size || 'default'],
              'ml-2 animate-spin',
              variant === 'default' ? 'text-white' : 'text-purple-500'
            )}
          />
        )}
      </Comp>
    )
  }
)
Button.displayName = 'Button'

export { Button, buttonVariants }
