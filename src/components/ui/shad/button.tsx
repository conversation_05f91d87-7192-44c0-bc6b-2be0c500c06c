import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/utils/shad'
import { MirrorLoading } from 'wujieai-react-icon'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-lg text-lg font-bold shadow-sm ring-offset-background transition-colors disabled:pointer-events-none disabled:opacity-50 active:brightness-90',
  {
    variants: {
      variant: {
        default: 'bg-gradient-primary btn-text-color shadow-button-primary',
        outline:
          'border border-3 maze-primary-text bg-transparent phone:border-[1px]',
        minor:
          'border border-neutral-900 bg-[rgba(255,255,255,0.56)] shadow-button-neutral text-primary',
      },
      size: {
        default: 'h-12 px-12 py-6',
        md: 'h-16 px-8 py-4 ',
        lg: 'h-20 px-12 py-6 ',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
)
type Size = 'default' | 'md' | 'lg'
const loadingSize: Record<Size, string> = {
  default: 'h-6 w-6',
  md: 'h-8 w-8 ',
  lg: 'h-10 w-10 ',
}

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant, size, asChild = false, loading = false, ...props },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button'
    // loading按钮禁用的样式
    if (loading) {
      props.disabled = true
    }
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {props.children}
        {loading && (
          <MirrorLoading
            className={cn(
              loadingSize[size || 'default'],
              'ml-2 text-neutral-900 animate-spin',
              { '!text-primary': variant === 'minor' }
            )}
          />
        )}
      </Comp>
    )
  }
)
Button.displayName = 'Button'

export { Button, buttonVariants }
