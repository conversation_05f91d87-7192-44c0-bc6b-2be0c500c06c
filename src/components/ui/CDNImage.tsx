import React from 'react'
import { IMAGE_CDN } from '@/configs'
import { isProdApi } from '@/utils'

export const CDNImage: React.FC<{
  src: string
  alt?: string
  className?: string
  style?: React.CSSProperties
  onClick?: () => void
}> = ({ src, alt = '', className, style, onClick }) => {
  return (
    <img
      className={className}
      style={style}
      src={isProdApi ? IMAGE_CDN + src : src}
      alt={alt}
      onClick={onClick}
    />
  )
}
