import classNames from 'classnames'
import styles from './MyStepper.module.css'
import { Minus, Plus } from 'wujieai-react-icon'

interface Props {
  value?: number
  onChange?: (n: number) => void
  step?: number
  min?: number
  max?: number
  plusDisabled?: boolean
  minusDisabled?: boolean
  className?: string
}

export default function MyStepper({
  value,
  onChange,
  step = 1,
  min = Number.MIN_SAFE_INTEGER,
  max = Number.MAX_SAFE_INTEGER,
  minusDisabled = false,
  plusDisabled = false,
  className,
}: Props) {
  const minDisabled = (value || 0) - step < min
  const maxDisabled = (value || 0) + step > max

  return (
    <div
      className={classNames(
        'flex items-center justify-center',
        styles.myStepper,
        className
      )}
      onClick={e => {
        e.stopPropagation()
      }}
    >
      <span
        className={classNames(
          'stepper-btn w-16 h-16 flex items-center justify-center rounded-full border-2 border-white bg-[rgba(255,255,255,0)] shadow-button-neutral',
          {
            'cursor-not-allowed text-neutral-400 opacity-[0.56] border-neutral-400':
              minDisabled || minusDisabled,
            'cursor-pointer text-neutral-50': !(minDisabled || minusDisabled),
          }
        )}
        onClick={() => {
          if (minDisabled || minusDisabled) {
            return
          }
          onChange?.((value ?? 0) - step)
        }}
      >
        <Minus size={32} className="font-bold text-white" />
      </span>
      <div className="mx-6 flex-1 text-center font-bold text-neutral-50 text-lg border-2 border-white text-white rounded-full py-3.5 bg-[rgba(255,255,255,0)] shadow-button-neutral">
        {value}
      </div>
      <span
        className={classNames(
          'stepper-btn w-16 h-16 flex items-center justify-center rounded-full border-2 border-white bg-[rgba(255,255,255,0)] shadow-button-neutral',
          {
            'cursor-not-allowed text-neutral-400 opacity-[0.56]':
              maxDisabled || plusDisabled,
            'cursor-pointer text-neutral-50': !(maxDisabled || plusDisabled),
          }
        )}
        onClick={() => {
          if (maxDisabled || plusDisabled) {
            return
          }

          onChange?.((value ?? 0) + step)
        }}
      >
        <Plus size={32} className="font-bold text-white" />
      </span>
    </div>
  )
}
