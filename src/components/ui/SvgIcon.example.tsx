import React from 'react'
import { SvgIcon } from './SvgIcon'

/**
 * SvgIcon组件使用示例
 * 展示如何使用SvgIcon组件替代img元素显示SVG图标
 */
export const SvgIconExample: React.FC = () => {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold mb-4">SvgIcon 组件使用示例</h1>

      {/* 基础使用 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">
          基础使用 - 需要设置容器尺寸
        </h2>
        <div className="flex items-center gap-4">
          <SvgIcon
            src="/images/icons/arrow-right.svg"
            alt="右箭头"
            className="w-8 h-8"
          />
          <SvgIcon
            src="/images/icons/arrow-left.svg"
            alt="左箭头"
            className="w-8 h-8"
          />
          <SvgIcon
            src="/images/icons/checked.svg"
            alt="选中"
            className="w-8 h-8"
          />
        </div>
      </section>

      {/* 不同尺寸 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">不同尺寸</h2>
        <div className="flex items-center gap-4">
          <SvgIcon
            src="/images/icons/arrow-right.svg"
            alt="大箭头"
            className="w-12 h-12"
            svgClassName="text-blue-500"
          />
          <SvgIcon
            src="/images/icons/arrow-left.svg"
            alt="中等箭头"
            className="w-8 h-8"
            svgClassName="text-red-500"
          />
          <SvgIcon
            src="/images/icons/checked.svg"
            alt="小选中"
            className="w-6 h-6"
            svgClassName="text-green-500"
          />
        </div>
      </section>

      {/* 点击事件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">点击事件</h2>
        <div className="flex items-center gap-4">
          <SvgIcon
            src="/images/icons/arrow-right.svg"
            alt="可点击箭头"
            className="w-10 h-10 cursor-pointer hover:bg-gray-100 p-2 rounded"
            onClick={() => alert('右箭头被点击了！')}
          />
          <SvgIcon
            src="/images/icons/close.svg"
            alt="关闭按钮"
            className="w-10 h-10 cursor-pointer hover:bg-red-100 p-2 rounded-full"
            svgClassName="text-red-600"
            onClick={() => alert('关闭按钮被点击了！')}
          />
        </div>
      </section>

      {/* 容器样式 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">容器样式</h2>
        <div className="flex items-center gap-4">
          <SvgIcon
            src="/images/icons/checked.svg"
            alt="带背景的图标"
            className="w-12 h-12 bg-blue-100 p-3 rounded-full"
            svgClassName="text-blue-600"
          />
          <SvgIcon
            src="/images/icons/tips.svg"
            alt="带边框的图标"
            className="w-12 h-12 border-2 border-gray-300 p-2 rounded"
            svgClassName="text-gray-600"
          />
        </div>
      </section>

      {/* 垂直居中测试 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">垂直居中测试</h2>
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-gray-200 border-2 border-dashed border-gray-400 relative">
            <SvgIcon
              src="/images/icons/checked.svg"
              alt="居中测试"
              className="w-8 h-8 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              svgClassName="text-green-500"
            />
          </div>
          <div className="w-20 h-12 bg-gray-200 border-2 border-dashed border-gray-400 flex items-center justify-center">
            <SvgIcon
              src="/images/icons/arrow-right.svg"
              alt="Flex居中"
              className="w-6 h-6"
            />
          </div>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          左边使用绝对定位居中，右边使用flex居中，都应该完美垂直居中
        </p>
      </section>

      {/* 移动设备优化示例 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">
          移动设备优化 - 响应式尺寸
        </h2>
        <div className="flex items-center gap-4">
          <SvgIcon
            src="/images/icons/arrow-right.svg"
            alt="响应式箭头"
            className="w-8 h-8 phone:w-12 phone:h-12"
          />
          <SvgIcon
            src="/images/icons/checked.svg"
            alt="响应式选中"
            className="w-6 h-6 phone:w-10 phone:h-10"
            svgClassName="text-green-500"
          />
        </div>
        <p className="text-sm text-gray-600 mt-2">
          在手机上这些图标会显示得更大，但不会超出容器范围
        </p>
      </section>

      {/* 加载状态处理 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">加载状态处理</h2>
        <div className="flex items-center gap-4">
          <SvgIcon
            src="/images/icons/nonexistent.svg"
            alt="不存在的图标"
            className="w-8 h-8"
            onError={() => console.log('SVG加载失败')}
          />
          <SvgIcon
            src="/images/icons/arrow-right.svg"
            alt="成功加载的图标"
            className="w-8 h-8"
            onLoad={() => console.log('SVG加载成功')}
          />
        </div>
      </section>

      {/* 禁用CDN */}
      <section>
        <h2 className="text-xl font-semibold mb-4">CDN设置</h2>
        <div className="flex items-center gap-4">
          <SvgIcon
            src="/images/icons/arrow-right.svg"
            alt="使用CDN（默认）"
            className="w-8 h-8"
          />
          <SvgIcon
            src="/images/icons/arrow-left.svg"
            alt="不使用CDN"
            className="w-8 h-8"
            useCDN={false}
          />
        </div>
      </section>
    </div>
  )
}

export default SvgIconExample
