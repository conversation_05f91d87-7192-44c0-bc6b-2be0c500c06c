@import 'tailwindcss' reference;

.autoScrollWrap {
  @apply flex justify-center;
}

.autoScroll {
  display: flex;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;
  overflow: auto;
  height: 100%;
  padding: 12px 0;
  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
}

.switchArrow {
  @apply absolute w-[52px] h-[52px] top-1/2 -translate-y-1/2 rounded-full transition-all z-10 cursor-pointer flex justify-center items-center;
  &:global(.prevButton) {
    @apply left-10;
  }
  &:global(.nextButton) {
    @apply right-10;
  }
  &:global(.disabled) {
    opacity: 0.24;
    @apply cursor-not-allowed;
  }
}
