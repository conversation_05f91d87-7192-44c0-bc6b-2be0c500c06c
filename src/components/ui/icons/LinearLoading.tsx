import classNames from 'classnames'

interface Props {
  className?: string
}

export default function LinearLoading({ className }: Props) {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={classNames(className)}
    >
      <path
        d="M3.33203 29.9948C3.33203 44.7224 15.2711 56.6615 29.9987 56.6615C44.7263 56.6615 56.6654 44.7224 56.6654 29.9948C56.6654 15.2672 44.7263 3.32812 29.9987 3.32812"
        stroke="url(#paint0_linear_10756_25040)"
        strokeWidth="5.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <defs>
        <linearGradient
          id="paint0_linear_10756_25040"
          x1="56.6654"
          y1="66.673"
          x2="4.27558"
          y2="2.55714"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#6964DE" />
          <stop offset="1" stopColor="#FCA6E9" />
        </linearGradient>
      </defs>
    </svg>
  )
}
