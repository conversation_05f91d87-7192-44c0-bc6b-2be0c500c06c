import classnames from 'classnames'
import styles from './MyTab.module.css'
import { useTranslation } from 'react-i18next'
function MyTab({
  items,
  activeKey,
  setActiveKey,
  className,
  type = 'default',
}: {
  items: { label: string; value: any }[]
  activeKey: any
  setActiveKey: (val: any) => void
  className?: string
  /** 类型：默认 ｜ 胶囊 */
  type?: 'default' | 'capsule'
}) {
  const { t } = useTranslation()
  return (
    <div
      className={classnames(
        styles.tabs,
        type === 'capsule' && styles.tabsCapsule,
        className
      )}
    >
      {items.map(it => (
        <div
          className={classnames(
            styles.tab,

            {
              [styles.tabActive]: it.value === activeKey,
            }
          )}
          key={it.value}
          onClick={() => setActiveKey(it.value)}
        >
          {t(it.label)}
        </div>
      ))}
    </div>
  )
}

export default MyTab
