# SvgIcon 组件

一个基于 `react-svg` 的 SVG 图标组件，用于替代项目中的 `img` 元素显示 SVG 图标。

## 特性

- ✅ 使用 `react-svg` 提供更好的 SVG 渲染和样式控制
- ✅ 支持 CDN 和本地路径
- ✅ 提供加载失败的降级方案
- ✅ 支持自定义样式和点击事件
- ✅ 完整的 TypeScript 类型支持
- ✅ 无障碍访问支持
- ✅ SVG 默认充满父元素，在移动设备上显示正确
- ✅ 完美的垂直居中，消除默认行高和基线影响

## 基础用法

```tsx
import { SvgIcon } from '@/components/ui/SvgIcon'

// 基础使用 - SVG会充满父元素
<div className="w-8 h-8">
  <SvgIcon 
    src="/images/icons/arrow-right.svg" 
    alt="右箭头" 
  />
</div>

// 自定义样式
<SvgIcon 
  src="/images/icons/checked.svg" 
  alt="选中" 
  svgClassName="text-green-500"
  className="w-6 h-6"
/>

// 点击事件
<SvgIcon 
  src="/images/icons/close.svg" 
  alt="关闭" 
  className="w-16 h-16 cursor-pointer hover:bg-gray-100 p-2 rounded"
  onClick={() => console.log('点击了关闭按钮')}
/>
```

## API

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `src` | `string` | - | SVG 文件路径，支持相对路径和绝对路径 |
| `alt` | `string` | `''` | 替代文本，用于无障碍访问 |
| `className` | `string` | - | 容器元素的 CSS 类名，建议设置宽高 |
| `svgClassName` | `string` | - | SVG 元素的 CSS 类名 |
| `style` | `React.CSSProperties` | - | 容器元素的内联样式 |
| `svgStyle` | `React.CSSProperties` | - | SVG 元素的内联样式 |
| `onClick` | `() => void` | - | 点击事件处理函数 |
| `useCDN` | `boolean` | `true` | 是否使用 CDN 加载图片 |
| `onError` | `() => void` | - | 加载失败时的回调函数 |
| `onLoad` | `() => void` | - | 加载成功时的回调函数 |

## 迁移指南

### 从 img 元素迁移

**之前：**
```tsx
import Icon_right from '/images/icons/arrow-right.svg'

<img src={Icon_right} alt="Next" className="w-24 h-24" />
```

**之后：**
```tsx
import { SvgIcon } from '@/components/ui/SvgIcon'

<SvgIcon 
  src="/images/icons/arrow-right.svg" 
  alt="下一页" 
  className="w-24 h-24"
/>
```

### 从 publicPreLoadSourceObj 迁移

**之前：**
```tsx
import { publicPreLoadSourceObj } from '@/configs/source'

<img src={publicPreLoadSourceObj.detailPrint} alt="" className="w-6 h-6" />
```

**之后：**
```tsx
import { SvgIcon } from '@/components/ui/SvgIcon'

<SvgIcon 
  src="/images/icons/detail/print.svg" 
  alt="打印" 
  className="w-6 h-6"
/>
```

## 注意事项

1. **容器尺寸**：SVG 会充满父容器，建议在 `className` 中设置容器的宽高
2. **移动设备优化**：组件已针对 iPhone 等移动设备优化，SVG 不会显示过大
3. **垂直居中优化**：组件已优化垂直居中，消除了默认行高和基线的影响
4. **样式应用**：使用 `svgClassName` 设置 SVG 元素样式，使用 `className` 设置容器样式
5. **CDN 支持**：默认启用 CDN，如需禁用请设置 `useCDN={false}`
6. **降级方案**：当 react-svg 加载失败时，会自动降级为普通的 img 元素
7. **无障碍访问**：建议始终提供有意义的 `alt` 属性

## 已完成的迁移

以下组件已经完成了从 img 元素到 SvgIcon 的迁移：

- ✅ `AutoScroll.tsx` - 左右箭头导航
- ✅ `MazePictureResult.tsx` - 轮播导航和选中状态图标
- ✅ `ModalCloseBtn.tsx` - 关闭按钮
- ✅ `ShootTipModal.tsx` - 提示图标
- ✅ `MazeSingleTemplateList.tsx` - 轮播导航
- ✅ `pages/event/start.tsx` - 提示图标

## 性能优化

- 使用 `react-svg` 可以更好地控制 SVG 的渲染和样式
- 支持 SVG 内联，减少网络请求
- 提供加载状态回调，便于实现加载动画
- SVG 自动充满容器，避免在不同设备上显示异常
- 优化的垂直居中，消除布局偏移和对齐问题
