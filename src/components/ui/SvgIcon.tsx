import React from 'react'
import { ReactSVG } from 'react-svg'
import { toCDNImage } from '@/utils'
import { cn } from '@/utils/shad'

/**
 * SVG图标组件 - 使用react-svg替代img元素显示SVG
 * 支持CDN和本地路径，提供更好的SVG渲染和样式控制
 * SVG默认充满父元素容器，适合在iPhone等移动设备上正确显示
 * 已优化垂直居中，消除默认行高和基线影响
 */
export const SvgIcon: React.FC<{
  /** SVG文件路径，支持相对路径和绝对路径 */
  src: string
  /** 替代文本 */
  alt?: string
  /** 容器类名 */
  className?: string
  /** SVG元素类名 */
  svgClassName?: string
  /** 容器样式 */
  style?: React.CSSProperties
  /** SVG元素样式 */
  svgStyle?: React.CSSProperties
  /** 点击事件 */
  onClick?: () => void
  /** 是否使用CDN，默认true */
  useCDN?: boolean
  /** 加载失败时的回调 */
  onError?: () => void
  /** 加载成功时的回调 */
  onLoad?: () => void
}> = ({
  src,
  alt = '',
  className,
  svgClassName,
  style,
  svgStyle,
  onClick,
  useCDN = true,
  onError,
  onLoad,
}) => {
  // 处理SVG路径，支持CDN
  const svgSrc = useCDN ? toCDNImage(src) : src

  return (
    <div
      className={cn(
        'w-full h-full flex items-center justify-center leading-none',
        className
      )}
      style={{
        lineHeight: 0, // 重置行高
        fontSize: 0, // 重置字体大小，避免基线影响
        ...style,
      }}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      aria-label={alt}
    >
      <ReactSVG
        src={svgSrc}
        beforeInjection={svg => {
          // 设置SVG属性，让SVG充满容器并完美居中
          svg.setAttribute('width', '100%')
          svg.setAttribute('height', '100%')

          // 确保SVG完美居中，消除任何默认的对齐影响
          svg.style.display = 'block'
          svg.style.verticalAlign = 'top'

          if (alt) {
            svg.setAttribute('aria-label', alt)
          }
          if (svgClassName) {
            svg.setAttribute('class', svgClassName)
          }
          if (svgStyle) {
            Object.assign(svg.style, svgStyle)
          }
        }}
        onError={error => {
          console.warn('SVG加载失败:', svgSrc, error)
          onError?.()
        }}
        onLoad={() => {
          onLoad?.()
        }}
        fallback={() => (
          // 加载失败时的降级方案
          <img
            src={svgSrc}
            alt={alt}
            className={cn('w-full h-full object-contain', svgClassName)}
            style={svgStyle}
          />
        )}
      />
    </div>
  )
}
