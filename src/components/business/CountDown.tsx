import React, { useEffect } from 'react'
import { useCountDown } from '@/hooks/useCountDown'
interface IProps {
  value: number
  onFinish?: () => void
  className?: string
}
const CountDown: React.FC<IProps> = ({ value, onFinish, className }) => {
  const { second, startSecond } = useCountDown(value, onFinish)

  useEffect(() => {
    startSecond()
  }, [])

  return <span className={className}>{second}</span>
}
export default CountDown
