import { useRouteError } from 'react-router-dom'
import SourceLoadStatus from '@/components/business/SourceLoadStatus'
import { useTranslation } from 'react-i18next'
interface IProps {
  errorText?: string
  loadStatus?: string
}
const ErrorBoundary: React.FC<IProps> = () => {
  const error = useRouteError()
  const { t } = useTranslation()
  console.log(error)
  return (
    <SourceLoadStatus
      loadStatus="failed"
      errorText={t('页面加载失败，请稍后重试')}
    />
  )
}
export default ErrorBoundary
