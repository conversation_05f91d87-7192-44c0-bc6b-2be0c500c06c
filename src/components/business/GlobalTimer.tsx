/**
 * 全局倒计时
 * 用于当用户长时间未操作时，自动返回首页
 */

import { useEffect, useMemo, useState } from 'react'
import { useCountDown } from '@/hooks/useCountDown'
import { useNavigate, useLocation } from 'react-router-dom'
import { MyModal } from '@/components/ui/MyModal'
import { GlobalNetwork } from './GlobalNetwork'
import { AFTER_PAY_ROUTES } from '@/configs'
import {
  isOfficialMerchantAtom,
  isTaskTypeSelectedAtom,
  silentConfigAtom,
  screenOrientationAtom,
} from '@/stores'
import { useAtom } from 'jotai'
import { useTranslation } from 'react-i18next'

/** 全局倒计时 */
const GLOBAL_TIMER_COUNT = 300

export const GlobalTimer = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const [open, setOpen] = useState(false)
  const [isOfficialMerchant] = useAtom(isOfficialMerchantAtom)
  const [, setIsTaskTypeSelected] = useAtom(isTaskTypeSelectedAtom)

  const [silentConfig] = useAtom(silentConfigAtom)
  const [screenOrientation] = useAtom(screenOrientationAtom)

  const { t } = useTranslation()

  // 是否配置静默页面
  const isConfigSilent = useMemo(() => {
    if (screenOrientation.isPortrait) {
      return silentConfig.portraitImages.length > 0
    }
    return silentConfig.images.length > 0
  }, [silentConfig, screenOrientation])

  const { second, resetSecond, clearSecond } = useCountDown(
    GLOBAL_TIMER_COUNT,
    () => {
      setOpen(false)
      setIsTaskTypeSelected(false)

      /** 备注：官方商户未配置静默页则返回首页 */
      if (isOfficialMerchant && !isConfigSilent) {
        navigate('/home')
        return
      }

      /** 如果当前页是首页，则切换到静默页 */
      if (location.pathname === '/home') {
        navigate('/silent')
      } else {
        navigate('/home')
      }
    }
  )
  const onResetSecond = () => {
    resetSecond(GLOBAL_TIMER_COUNT)
  }

  useEffect(() => {
    /** 有以下操作都重置定时器 */
    document.addEventListener('click', onResetSecond) // 点击
    document.addEventListener('touchstart', onResetSecond) // 触摸

    return () => {
      document.removeEventListener('click', onResetSecond)
      document.removeEventListener('touchstart', onResetSecond)
    }
  }, [])

  useEffect(() => {
    // 静默页 无定时器
    if (location.pathname === '/silent') return
    onResetSecond()
  }, [location.pathname])

  useEffect(() => {
    /** 是否是付款后的页面 */
    const isAfterPayRoute = AFTER_PAY_ROUTES.includes(location.pathname)

    if (second === 60 && isAfterPayRoute) {
      setOpen(true)
    } else if (second > 60 && isAfterPayRoute) {
      setOpen(false)
    }
  }, [second])

  return (
    <>
      <MyModal
        open={open}
        title={t('超时提醒')}
        content={
          <span className="text-center text-lg leading-[42px]">
            {t('因您长时间未操作')}
            <br />
            {t(`系统将在 {{second}}s 后自动返回首页`, { second })}
            <br />
            <span className="text-error">
              {t('请注意：返回首页后所有操作记录都将丢失')}
            </span>
          </span>
        }
        showCancelButton={false}
        okText={t('继续流程')}
        onOk={() => {
          setOpen(false)
          onResetSecond()
        }}
        onCancel={() => {
          setOpen(false)
          onResetSecond()
        }}
      />
      <GlobalNetwork
        onError={() => {
          clearSecond()
        }}
        onOk={() => {
          onResetSecond()
        }}
      />
    </>
  )
}
