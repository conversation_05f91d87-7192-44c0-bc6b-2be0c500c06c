import { useNavigate } from 'react-router-dom'
import { MyModal } from '../ui/MyModal'
import CountDown from './CountDown'
import { useTranslation } from 'react-i18next'

export const QuestionModal: React.FC<{
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}> = ({ open, setOpen }) => {
  const navigator = useNavigate()
  const { t } = useTranslation()

  const onCloseQuestion = () => {
    navigator('/home')
    setOpen(false)
  }

  return (
    <MyModal
      title={t('服务出现问题')}
      open={open}
      content={
        <div className="text-[24px] text-center text-neutral-400 font-bold leading-[42px]">
          {t('抱歉，服务暂时出现问题。')}
          {/* <br /> */}
          {/* 请联系客服申请退款，我们将在 1-3 个工作日内处理。感谢您的理解与支持。 */}
        </div>
      }
      onOk={onCloseQuestion}
      okText={t('知道了')}
      showCancelButton={false}
      footerExtra={
        <div className="text-center text-neutral-50 font-bold text-[24px] leading-[32px] mt-6">
          <CountDown
            className="text-gradient-primary"
            value={60}
            onFinish={onCloseQuestion}
          />
          <span className="text-gradient-primary mr-4">s</span>
          {t('后自动返回首页')}
        </div>
      }
    />
  )
}
