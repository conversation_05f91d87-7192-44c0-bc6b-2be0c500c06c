/**
 * 1.预加载loading、结果
 * 2.js错误页
 * 3.路由404
 */
import React, { useEffect } from 'react'
import { Button } from '@/components/ui/shad/button'
import { isIPad, isIphone, isWebApp, isWebAppDevice, toCDNImage } from '@/utils'
import { MirrorLoading } from 'wujieai-react-icon'
import { useTranslation } from 'react-i18next'
import { Direction } from '@/graphqls/types'
import { LANGUAGE } from '@/configs'
import { useDevice } from '@/hooks/useDevice'
import { useMerchant } from '@/hooks/useMerchant'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'
import { toast } from '@/components/ui/shad/use-toast'

interface IProps {
  errorText?: string
  loadStatus?: string
  style?: React.CSSProperties
  onReload?: () => void
  isReloadBtn?: boolean
}
const SourceLoadStatus: React.FC<IProps> = ({
  loadStatus,
  errorText,
  style,
  onReload = () => {
    window.location.href = '/'
  },
  isReloadBtn = true,
}) => {
  const { t } = useTranslation()
  const { getDefaultDeviceInfo } = useDevice()
  const { getDefaultMerchantInfo } = useMerchant()

  useEffect(() => {
    const direction =
      window.innerHeight > window.innerWidth
        ? Direction.VERTICAL
        : Direction.CROSSWISE
    if (loadStatus === 'undistributed') {
      initDefaultRes(
        isIPad() || isIphone() || isWebApp() || isWebAppDevice()
          ? Direction.VERTICAL
          : direction
      )
    }
  }, [loadStatus])

  const initDefaultRes = async (type: Direction) => {
    const deviceInfo = await getDefaultDeviceInfo()
    const merchantInfo = await getDefaultMerchantInfo()
    const eventListRes = await _ajax.get(_api.event_list)
    const eventId = eventListRes?.data?.data?.data[0]?.id
    console.log('register_device', type, deviceInfo, merchantInfo, eventListRes)
    const data = {
      device_id: deviceInfo?.id,
      direction: type,
      merchant_no: merchantInfo?.no,
      event_id: eventId,
    }
    const res = await _ajax.post(_api.register_device, data, {
      params: { language: LANGUAGE },
    })
    if (res.data.code === 200) {
      onReload()
    } else {
      toast({
        description: res?.data?.msg || '未知错误，请稍后重试',
      })
    }
  }

  return (
    <div
      className="flex justify-center items-center h-full w-full bg-no-repeat bg-center bg-cover"
      style={style}
    >
      <div className="w-full h-full bg-text-color flex justify-center items-center text-6xl">
        {loadStatus === 'loading' ? (
          <div className="flex flex-col justify-center items-center">
            <div className="relative w-[6rem] h-[6rem] mb-8">
              <MirrorLoading className="w-full h-full absolute left-0 top-0 animate-spin maze-primary-text" />
            </div>
            <div className="text-[2.5rem] leading-[2.5rem] font-bold maze-primary-text">
              {t('资源加载中，请稍候')}
            </div>
          </div>
        ) : loadStatus === 'undistributed' ? (
          <div className="flex flex-col justify-center items-center">
            <div className="relative w-[6rem] h-[6rem] mb-8">
              <MirrorLoading className="w-full h-full absolute left-0 top-0 animate-spin maze-primary-text" />
            </div>
            <div className="text-[2.5rem] leading-[2.5rem] font-bold maze-primary-text">
              {t('资源加载中，请稍候')}...
            </div>
          </div>
        ) : (
          <div className="flex flex-col justify-center items-center ">
            <div className=" w-[6rem] h-[6rem] mb-8">
              <img
                src={toCDNImage('/images/icons/<EMAIL>')}
                alt=""
                className="w-full h-full"
              />
            </div>
            <div className="text-[2rem] leading-[2.5rem] font-bold text-ellipsis maze-primary-text">
              {errorText || t('资源加载失败')}
            </div>
            {isReloadBtn && (
              <Button
                onClick={onReload}
                size="lg"
                className="mt-12 w-[24rem]"
                variant="outline"
              >
                {t('重新加载')}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
export default SourceLoadStatus
