import { useVideoTask } from '@/components/pages/video/hooks/useVideoTask'
import { useEffect } from 'react'
import { allVideoTemplateAtom } from '@/components/pages/video/videoStore'
import { useAtomValue } from 'jotai'
function Video() {
  const allVideoTemplate = useAtomValue(allVideoTemplateAtom)
  const { playDefaultVideo } = useVideoTask()
  useEffect(() => {
    playDefaultVideo(allVideoTemplate?.map(it => it.showUrl!) || [])
  }, [])
  return (
    <>
      <div className="w-full h-full bg-neutral-50"></div>
    </>
  )
}

export default Video
