import { Swiper, SwiperSlide } from 'swiper/react'
import { Autoplay, EffectFade } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/autoplay'
import classNames from 'classnames'
import styles from './index.module.css'
import { useNavigate } from 'react-router-dom'
import { isMachine, to800Image, toVideoPoster, validateIsVideo } from '@/utils'
import { useAtom, useAtomValue } from 'jotai'
import { screenOrientationAtom, silentConfigAtom } from '@/stores'
import { useEffect, useMemo } from 'react'
import { PortalContainer } from '@/components/ui/PortalContainer'
import { useBridge } from '@/hooks/useBridge'
import { publicPreLoadSourceObj } from '@/configs/source'
import StartPage from '@/pages/event/start'

const DEFAULT_IMAGES = [publicPreLoadSourceObj.silentCrosswiseBgImg]

function Silent() {
  const navigate = useNavigate()
  const [silentConfig] = useAtom(silentConfigAtom)
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const { showVideo, hiddenVideo } = useBridge()

  const silentImages = useMemo(() => {
    if (screenOrientation.isPortrait) {
      if (silentConfig.portraitImages.length > 0) {
        return silentConfig.portraitImages
      }
      return [publicPreLoadSourceObj.silentVerticalBgImg]
    }
    if (silentConfig.images.length > 0) {
      return silentConfig.images
    }
    return DEFAULT_IMAGES
  }, [silentConfig, screenOrientation])

  // 视频和图片不会同时存在
  const { isVideo, videoUrl } = useMemo(() => {
    const index = silentImages.findIndex(it => validateIsVideo(it))
    return { isVideo: index !== -1, videoUrl: silentImages[index] }
  }, [silentImages])

  useEffect(() => {
    if (isVideo) {
      console.log('showVideo')
      showVideo({
        url: videoUrl,
        position: screenOrientation.isLandScape
          ? {
              leftTop: [0, 0],
              rightBottom: [1920, 1080],
            }
          : {
              leftTop: [0, 0],
              rightBottom: [1080, 1920],
            },
      })
    } else {
      console.log('hiddenVideo')
      hiddenVideo()
    }
  }, [videoUrl, isVideo])

  return <StartPage />
}

export default Silent
