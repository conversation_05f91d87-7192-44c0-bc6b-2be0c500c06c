.swiper {
  :global {
    .swiper-wrapper {
      transform-style: initial;
    }
    .swiper.swiper-coverflow {
      overflow: visible;
    }
    .swiper-slide {
      width: 100% !important;
      transition-property: all;
      transform-style: initial;
    }
    /* .swiper-slide-prev {
      transform-origin: 80%;
    }
    .swiper-slide-next {
      transform-origin: 20%;
    }

    .swiper-slide-prev,
    .swiper-slide-next {
      opacity: 0.32;
      scale: 0.8;
    } */
    .swiper-button-prev {
      width: 1000px;
      left: -1000px;
      height: 100%;
      top: 0;

      &:after {
        content: '';
      }
    }
    .swiper-button-next {
      width: 1000px;
      right: -1000px;
      height: 100%;
      top: 0;

      &:after {
        content: '';
      }
    }
  }
}