/** 拍摄页面 */
import BackToHome from '@/components/business/BackToHome'
import { MyContainer } from '@/components/ui/MyContainer'
import { ImageDetail } from '@/components/pages/detail/ImageDetail'
import { useTranslation } from 'react-i18next'

function Photo() {
  const { t } = useTranslation()
  return (
    <>
      <BackToHome
        onShowDialog={() => true}
        dialogTitle={t('提示')}
        dialogContent={
          <div className="text-center leading-[180%]">
            <div>{t('您确认返回首页吗？')}</div>
          </div>
        }
      />
      <MyContainer>
        <ImageDetail />
      </MyContainer>
    </>
  )
}

export default Photo
