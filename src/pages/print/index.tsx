import {
  drawPay<PERSON>rder<PERSON><PERSON>,
  isPaidVersionAtom,
  machineInfoAtom,
  printPayOrderAtom,
  resultOrderAtom,
  resultImagesAtom,
  screenOrientationAtom,
} from '@/stores'
import { useAtom, useAtomValue } from 'jotai'
import { MyImage } from '@/components/ui/MyImage'
import { Button } from '@/components/ui/shad/button'
import MyStepper from '@/components/ui/MyStepper'
import { useEffect, useMemo, useRef, useState } from 'react'
import { fenToYuan, graphQLErrorMessage, toRotateImage } from '@/utils'
import BackToHome from '@/components/business/BackToHome'
import { MirrorPicture } from 'wujieai-react-icon'

import PrintPaymentModal from '@/components/pages/payment/PrintPaymentModal'
import { useBridge } from '@/hooks/useBridge'
import {
  Direction,
  OrderStatus,
  PrintOrderInput,
  PrintOrderPayInfoFragment,
  PrintTemplateSize,
  useReportPrintSuccessMutation,
} from '@/graphqls/types'
import {
  ErrorModal,
  LoadingModal,
  SuccessModal,
} from '@/components/pages/print/LoadingModal'
import { useToast } from '@/components/ui/shad/use-toast'
import { usePayOrder } from '@/hooks/usePayOrder'
import { useValidResourceChange } from '@/hooks/useValidResourceChange'
import { useDebounce } from '@/hooks/useDebounce'
import { MyContainer } from '@/components/ui/MyContainer'
import { PRINTER_TYPE } from '@/configs'
import { PrinterStatus } from '@/stores/types'
import classNames from 'classnames'
import { useTranslation } from 'react-i18next'

export const roateImage = (url?: string, direction?: Direction) => {
  if (direction === Direction.VERTICAL) return url
  return toRotateImage(url!)
}

function Print() {
  const screenOrientation = useAtomValue(screenOrientationAtom)
  const [resultImages, setResultImages] = useAtom(resultImagesAtom)
  const [resultOrder] = useAtom(resultOrderAtom)

  const [machineInfo] = useAtom(machineInfoAtom)
  const [printOrderInfo] = useAtom(printPayOrderAtom)
  const [drawOrderInfo] = useAtom(drawPayOrderAtom)
  const [isPaidVersion] = useAtom(isPaidVersionAtom)
  const [isLoading, setIsLoading] = useState(false)

  /** 超时定时器恢复weappPrinting状态 */
  const timeoutRef = useRef<any>(null)

  /** 打印错误的childOrder */
  const [printErrorOrder, setPrintErrorOrder] =
    useState<PrintOrderPayInfoFragment>({})
  /** loading 弹窗 */
  const [loadingOpen, setLoadingOpen] = useState(false)
  /** 打印完成 */
  const [printDone, setPrintDone] = useState(false)
  /** 打印错误弹窗 */
  const [printError, setPrintError] = useState(false)
  /** 支付弹窗 */
  const [payOpen, setPayOpen] = useState(false)
  const { createPrintPayOrder } = usePayOrder()

  /** 免费打印次数是否已使用 */
  const [freePrintUsed, setFreePrintUsed] = useState(false)

  /** 错误失败重试次数 */
  const [retryCount, setRetryCount] = useState(0)

  const { t } = useTranslation()

  const {
    refetchResource,
    isResourceChange,
    ResourceChangeModal,
    isPrintOrderChange,
  } = useValidResourceChange()

  const { printImage } = useBridge()
  const { toast } = useToast()

  const [reportPrintSuccessAction] = useReportPrintSuccessMutation()

  const printImages = useMemo(() => {
    return resultImages.filter(item => (item.print ?? 0) >= 1)
  }, [resultImages])

  //** 打印总数 */
  const printNums = useMemo(() => {
    return resultImages.reduce((p, c) => {
      return p + (c?.print ?? 0)
    }, 0)
  }, [resultImages])

  const linkedPriner = useMemo(() => {
    return machineInfo.printers?.filter(
      it => it.printerStatus === PrinterStatus.AVAILABLE
    )?.[0]
  }, [machineInfo])
  /** 打印总数不超过打印纸 */
  const plusDisabled = useMemo(() => {
    return printNums >= (linkedPriner?.printerPaperNum ?? 0)
  }, [linkedPriner, printNums])

  /** 至少一张 */
  const minusDisabled = useMemo(() => {
    return printNums <= 1
  }, [printNums])

  const reportPrintStatus = async ({
    res,
    cbRes,
    fallbackFailMessage,
  }: {
    res: PrintOrderPayInfoFragment
    cbRes?: any
    fallbackFailMessage?: string
  }) => {
    const orderIds = res?.order?.printInfoList
      ?.map(it => it.childOrderId)
      ?.filter((_, i) => {
        return !!cbRes?.results?.[i]?.success
      })

    let failMessage = fallbackFailMessage
    // 如果有错误，上报
    if (cbRes?.results?.some((it: any) => !it.success)) {
      failMessage = JSON.stringify(
        cbRes?.results?.filter((it: any) => !it.success)
      )
    }

    await reportPrintSuccessAction({
      variables: {
        printOrderId: res?.order?.id,
        // 空数组上报表示全失败
        ids: orderIds?.length ? orderIds : [],
        failMessage,
      },
    }).catch(() => {})
    // 错误的记录 重新打印
    if (cbRes?.results?.some((it: any) => !it.success)) {
      setLoadingOpen(false)
      setPrintError(true)
      setPrintErrorOrder({
        ...res,
        order: {
          ...res.order,
          printInfoList: res?.order?.printInfoList?.filter((_, i) => {
            return !cbRes?.results?.[i]?.success
          }),
        },
      })
    } else {
      setTimeout(() => {
        // 全部成功
        setLoadingOpen(false)
        setPrintDone(true)
      }, 5000)
    }
  }

  const toPrint = async (res: PrintOrderPayInfoFragment) => {
    try {
      setLoadingOpen(true)

      try {
        printImage({
          content: res?.order?.printInfoList?.map(it => {
            // 订单中的打印图还是原图，自己获取下生成的打印图
            const previewUrl = resultImages?.filter(
              o => o.id === it.mirrorAiTask?.id
            )?.[0]?.previewUrl
            const imageUrl =
              it.mirrorAiTask?.editResultUrls?.[2] ??
              previewUrl ??
              (it?.mirrorAiTask?.resultUrl as string)
            const direction =
              resultImages?.filter(o => o.id === it.mirrorAiTask?.id)?.[0]
                ?.direction ?? Direction.VERTICAL

            return {
              imageUrl: roateImage(imageUrl, direction),
              count: 1,
              type: PRINTER_TYPE[PrintTemplateSize.SIX_INCHES],
            }
          }),
          callback: async (cbRes: any) => {
            await reportPrintStatus({
              res,
              cbRes,
            })
            clearTimeout(timeoutRef.current)
          },
        })
        /** 兜底3分钟打印机不回调callback，能主动上报失败，并且弹窗提示*/
        timeoutRef.current = setTimeout(
          () => {
            reportPrintStatus({
              res,
              fallbackFailMessage: '前端：兜底3分钟打印机不回调',
            })
          },
          3 * 60 * 1000
        )
      } catch (error) {
        console.log(error, 'error')
      }
    } catch (error) {
      setLoadingOpen(false)
      setPrintError(true)
    }
  }
  console.log('resultOrder', resultOrder)
  /** 创建订单参数 */
  const printOrderParam = useMemo((): PrintOrderInput => {
    return {
      price:
        (printNums - (freePrintUsed ? 0 : 1)) *
        resultOrder?.printPriceSnapshot?.sixPrice,
      printInfoList: printImages.map(it => {
        return {
          mirrorAiTaskId: it.id,
          num: it.print!,
          size: PrintTemplateSize.SIX_INCHES,
        }
      }),
      orderId: drawOrderInfo.order?.id,
    }
  }, [printNums, printImages, drawOrderInfo, freePrintUsed, resultOrder])

  const onPrint = useDebounce(async () => {
    try {
      console.log(printImages, 'printImages')
      if (isLoading) return
      setIsLoading(true)
      const res = await createPrintPayOrder({
        param: printOrderParam,
      })
      if (res.order?.status === OrderStatus.UNPAID) {
        setPayOpen(true)
      } else {
        toPrint(res)
      }
    } catch (error: any) {
      if (isResourceChange(error)) {
        await refetchResource()
      } else if (isPrintOrderChange(error)) {
        setFreePrintUsed(true)
      } else {
        toast({
          description: graphQLErrorMessage(error) || '下单失败，请稍后重试',
        })
      }
    } finally {
      setIsLoading(false)
    }
  }, [printOrderParam])

  /** 只有下单打印触发报错，freePrintUsed才可能变更；需要无感再次发起打印 */
  useEffect(() => {
    if (!freePrintUsed) return
    onPrint()
  }, [freePrintUsed])

  useEffect(() => {
    return () => {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [])

  console.log(printErrorOrder, 'printErrorOrder')

  return (
    <>
      <BackToHome
        onShowDialog={() => true}
        dialogTitle="提示"
        dialogContent={
          <div className="text-center leading-[180%]">
            <div>{t('您确认不需要打印，')}</div>
            <div>{t('直接结束流程并返回首页吗？')}</div>
          </div>
        }
      />
      <MyContainer className="flex items-center justify-center">
        <div className="py-[100px]">
          <div
            className={classNames(
              'flex justify-center items-center',
              screenOrientation.isPortrait && 'flex-wrap gap-8'
            )}
          >
            {resultImages.map(item => (
              <div
                className="mx-4"
                key={item.id}
                onClick={() => {
                  if ((item?.print ?? 0) > 0) return
                  if (plusDisabled) {
                    toast({
                      description: t('打印数量大于剩余打印纸数量'),
                    })
                    return
                  }
                  setResultImages(images => {
                    return images.map(it => {
                      if (item.id === it.id) {
                        return {
                          ...it,
                          print: (item?.print || 0) + 1,
                        }
                      }
                      return it
                    })
                  })
                }}
              >
                <div className="p-4 bg-white rounded-3xl mb-6">
                  <MyImage
                    src={
                      item.editResultUrls?.[2] ??
                      item.previewUrl ??
                      item.resultUrl
                    }
                    isAppCache={false}
                    className="w-[360px] rounded-3xl border border-solid border-neutral-50/5"
                    tag="v1200"
                  />
                </div>
                {(item.print ?? 0) >= 1 ? (
                  <MyStepper
                    value={item.print}
                    min={0}
                    max={linkedPriner?.printerPaperNum}
                    plusDisabled={plusDisabled}
                    minusDisabled={minusDisabled}
                    className="mx-6"
                    onChange={value => {
                      setResultImages(images => {
                        return images.map(it => {
                          if (item.id === it.id) {
                            return {
                              ...it,
                              print: value,
                            }
                          }
                          return it
                        })
                      })
                    }}
                  />
                ) : (
                  <div className="mx-6">
                    <Button size="md" className="block w-full" variant="minor">
                      <span className="text-gradient-primary">
                        {t('点击加印')}
                      </span>
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
          {printNums >= 1 && (
            <div className="flex justify-center mt-24">
              <Button
                size="lg"
                ga-data="startPrint"
                onClick={onPrint}
                loading={isLoading}
              >
                {isPaidVersion && (
                  <span className="text-xl mr-6">
                    <span className="text-lg mr-1 font-normal">&yen;</span>
                    {fenToYuan(
                      (printNums - (freePrintUsed ? 0 : 1)) *
                        resultOrder?.printPriceSnapshot?.sixPrice
                    )}
                  </span>
                )}
                <MirrorPicture className="mr-2" size={40} />
                <span>{t('立即打印')}</span>
              </Button>
            </div>
          )}
        </div>
        <PrintPaymentModal
          visible={payOpen}
          setVisible={setPayOpen}
          onSuccess={() => {
            setPayOpen(false)
            toPrint(printOrderInfo)
          }}
          printOrderParam={printOrderParam}
        />
        <LoadingModal open={loadingOpen} setOpen={setLoadingOpen} />
        <ErrorModal
          open={printError}
          setOpen={setPrintError}
          onRetry={() => {
            toPrint(printErrorOrder)
            setRetryCount(count => count + 1)
          }}
          retryCount={retryCount}
        />
        <SuccessModal open={printDone} />
        <ResourceChangeModal />
      </MyContainer>
    </>
  )
}

export default Print
