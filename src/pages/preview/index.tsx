import classNames from 'classnames'
import styles from './index.module.css'
import { AnalyticsScript } from '@/components/business/AnalyticsScript'
import { DrawItemFragment, MirrorSexEnum } from '@/graphqls/types'
import { useEffect, useMemo, useState } from 'react'
import resourceTemplate from '@/components/pages/preview/template.json'
import { MirrorCamera } from 'wujieai-react-icon'
import { Button } from '@/components/ui/shad/button'
import { MyContainer } from '@/components/ui/MyContainer'
import { AutoScroll, AutoScrollArrow } from '@/components/ui/AutoScroll'
import { getScaleStyle, toCDNImage } from '@/utils'
import SingleTemplate from '@/components/pages/homepage/SingleTemplate'
import MyTab from '@/components/ui/MyTab'
import { useThemeConfig } from '@/hooks/useThemeConfig'
import { useAtomValue } from 'jotai'
import { scaleAtom } from '@/stores'
import { useTranslation } from 'react-i18next'
import { ThemeDetail } from '@/apis/types'

const genders = [
  {
    label: '女',
    value: MirrorSexEnum.FEMALE,
  },
  {
    label: '男',
    value: MirrorSexEnum.MALE,
  },
]

const Preview = () => {
  // 选中的模板
  const [activeTemplate, setActiveTemplate] = useState<
    DrawItemFragment | undefined | null
  >()
  // 选中的性别
  const [activeGender, setActiveGender] = useState(MirrorSexEnum.FEMALE)
  // 选中的分类id
  const [activeTab, setActiveTab] = useState<number | undefined>(undefined)
  const scale = useAtomValue(scaleAtom)
  const { getPreviewThemeConfig, setBodyBgStyle, injectGlobalStyles } =
    useThemeConfig()

  const themeConfig = getPreviewThemeConfig()
  const { t } = useTranslation()

  const categoryList = useMemo(() => {
    return resourceTemplate?.map(it => ({
      label: it.name as string,
      value: it.id as number,
    }))
  }, [resourceTemplate])

  const selectTemplateList = useMemo(() => {
    return (resourceTemplate
      ?.find(it => it.id === activeTab)
      ?.itemList?.filter(it => it?.sex === activeGender) ||
      []) as DrawItemFragment[]
  }, [resourceTemplate, activeGender, activeTab])

  const activeTemplateIndex = useMemo(() => {
    const index = selectTemplateList?.findIndex(
      it => it?.id === activeTemplate?.id
    )
    return index < 0 ? 0 : index
  }, [selectTemplateList, activeTemplate?.id])

  /** 设置默认值 */
  useEffect(() => {
    setActiveTab(categoryList?.[0]?.value)
  }, [categoryList])
  useEffect(() => {
    setActiveTemplate(selectTemplateList?.[0])
  }, [selectTemplateList])

  useEffect(() => {
    injectGlobalStyles(themeConfig)
  }, [])
  useEffect(() => {
    setBodyBgStyle(themeConfig)
  }, [themeConfig])
  return (
    <>
      <div
        className={classNames(styles.container, 'min-w-[1920px] h-[1080px]')}
        style={{
          ...getScaleStyle({
            transform: `scale(${scale})`,
          }),
        }}
      >
        <div className="h-full w-full flex justify-center items-center">
          {/* 高度适配，宽度100%的方式 */}
          <div className="w-full h-[1080px]">
            {themeConfig.logoUrl && (
              <div className="fixed top-20 right-6">
                <img
                  src={themeConfig.logoUrl}
                  alt="logo"
                  className="w-[96px] h-[96px] object-cover rounded-full"
                />
              </div>
            )}
            <MyContainer>
              <div className="py-16">
                <div className="flex justify-center relative z-10 px-32 w-full">
                  <MyTab
                    items={genders}
                    activeKey={activeGender}
                    setActiveKey={val => setActiveGender(val)}
                    className="mr-16"
                  />
                  {/** 为了兼容分类比较多的场景 */}
                  <div
                    style={{
                      maxWidth: 'calc(100% - 200px)',
                    }}
                  >
                    <AutoScroll
                      activeIndex={categoryList.findIndex(
                        it => it.value === activeTab
                      )}
                      wrapClassName="p-3 rounded-full border-4 border-light-primary bg-white"
                      key="categoryList"
                    >
                      <div className="flex">
                        {categoryList.map(it => (
                          <div
                            className={classNames(
                              'text-lg font-bold px-6 py-2 rounded-full cursor-pointer whitespace-nowrap',
                              {
                                'btn-text-color bg-gradient-primary':
                                  it.value === activeTab,
                                'text-neutral-50': it.value !== activeTab,
                              }
                            )}
                            key={it.value}
                            onClick={() => setActiveTab(it.value)}
                          >
                            {it.label}
                          </div>
                        ))}
                      </div>
                    </AutoScroll>
                  </div>
                </div>
                <div className="relative -mt-[120px] w-[1920px]">
                  <AutoScroll
                    activeIndex={activeTemplateIndex}
                    key={`${activeGender}-${activeTab}`}
                  >
                    <div className="flex px-[128px] p-[180px]">
                      {selectTemplateList.length ? (
                        <>
                          {selectTemplateList.map((it, i) => (
                            <SingleTemplate
                              key={i}
                              item={it as ThemeDetail}
                              active={it?.id === activeTemplate?.id}
                              onSelect={() => setActiveTemplate(it)}
                            />
                          ))}
                        </>
                      ) : (
                        <div className="my-[34px] p-4 rounded-3xl bg-white text-center shadow-box-primary">
                          <div className="px-[88px] py-[117px] bg-neutral-700 rounded-2xl">
                            <img
                              className="w-24"
                              src={toCDNImage('/images/common/grinning.png')}
                              alt=""
                            />
                          </div>
                          <div className="text-lg font-bold text-neutral-400 mt-4">
                            {t('当前分类下还没有模板')}
                            <br />
                            {t('去看看别的分类吧')}
                          </div>
                        </div>
                      )}
                    </div>
                  </AutoScroll>
                  {selectTemplateList.length > 5 && (
                    <AutoScrollArrow
                      canPrev={activeTemplateIndex !== 0}
                      canNext={
                        activeTemplateIndex !== selectTemplateList.length - 1
                      }
                      onPrev={() => {
                        setActiveTemplate(
                          selectTemplateList[activeTemplateIndex - 1]
                        )
                      }}
                      onNext={() => {
                        setActiveTemplate(
                          selectTemplateList[activeTemplateIndex + 1]
                        )
                      }}
                    />
                  )}
                </div>
                <div className="flex flex-col items-center -mt-[90px] relative z-10">
                  {activeTemplate ? (
                    <>
                      <Button size="lg" className="flex" ga-data="preOrder">
                        <span className="mr-2 mt-1">¥</span>
                        <span className="mr-6 text-xl">9.99</span>
                        <MirrorCamera
                          size={40}
                          className="btn-text-color mr-2"
                        />
                        {t('立即开拍')}
                      </Button>
                      <div className="text-base bg-text-color mt-4 opacity-[0.56]">
                        {t('此价格包含 4 张 AI 图片以及 1 张打印费用')}
                      </div>
                    </>
                  ) : (
                    <Button size="lg" className="flex opacity-[0.56]">
                      {t('当前分类下暂无模板')}
                    </Button>
                  )}
                </div>
              </div>
            </MyContainer>
          </div>
        </div>
      </div>
      <AnalyticsScript />
    </>
  )
}

export default Preview
