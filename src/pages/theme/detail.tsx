/**
 * 主题详情页
 */
import BackToHome from '@/components/business/BackToHome'
import { MyContainer } from '@/components/ui/MyContainer'
import { useTranslation } from 'react-i18next'
import { MazeThemeDetail } from '@/components/pages/theme/ThemeDetail'
import PrintQrModal from '@/components/business/PrintQrModal'

function ThemeDetailPage() {
  const { t } = useTranslation()

  return (
    <>
      <BackToHome
        onShowDialog={() => true}
        dialogTitle={t('提示')}
        dialogContent={
          <div className="text-center leading-[180%]">
            <div>{t('您确认返回首页吗？')}</div>
          </div>
        }
      />
      <PrintQrModal />
      <MyContainer>
        <MazeThemeDetail />
      </MyContainer>
    </>
  )
}

export default ThemeDetailPage
