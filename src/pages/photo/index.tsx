/** 拍摄页面 */
import { useAtom } from 'jotai'
import { cameraEnableAtom, machineInfoAtom } from '@/stores'
import BackToHome from '@/components/business/BackToHome'

import PhotoHasCamera from '@/components/pages/photo/hasCamera/PhotoHasCamera'
import PhotoNoCamera from '@/components/pages/photo/noCamera/PhotoNoCamera'
import { MyContainer } from '@/components/ui/MyContainer'
import { useMemo } from 'react'
import { CameraStatus } from '@/stores/types'
import { isMirror } from '@/utils'
import { useTranslation } from 'react-i18next'

function Photo() {
  const [cameraEnable] = useAtom(cameraEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const { t } = useTranslation()
  const hasCamera = useMemo(() => {
    console.log(
      'photo page HasCamera: ',
      cameraEnable,
      machineInfo,
      'isMirror',
      isMirror()
    )
    return (
      cameraEnable &&
      ((isMirror() && machineInfo.cameraStatus === CameraStatus.AVAILABLE) ||
        !isMirror())
    )
  }, [cameraEnable, machineInfo, isMirror])

  console.log('photo, hasCamera2: ', hasCamera)

  return (
    <>
      <BackToHome
        onShowDialog={() => true}
        dialogTitle={t('提示')}
        dialogContent={
          <div className="text-center leading-[180%]">
            <div>{t('您确认返回首页吗？')}</div>
          </div>
        }
      />
      <MyContainer>
        {hasCamera ? <PhotoHasCamera /> : <PhotoNoCamera />}
      </MyContainer>
    </>
  )
}

export default Photo
