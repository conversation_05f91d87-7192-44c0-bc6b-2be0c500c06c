import BackToHome from '@/components/business/BackToHome'
import { useNavigate } from 'react-router-dom'
import { Input } from '@/components/ui/shad/input'
import { Button } from '@/components/ui/shad/button'
import { useState } from 'react'

function CameraTest() {
  const [localHref, setLocalHref] = useState('')
  const navigator = useNavigate()

  return (
    <>
      <BackToHome onShowDialog={() => true} dialogTitle="提示" />

      <div
        className="fixed bottom-4 right-[90px] text-sm"
        onClick={() => {
          navigator('/photo')
        }}
      >
        切换摄像头页面
      </div>
      <div
        className="fixed bottom-4 right-[290px] text-sm"
        onClick={() => {
          window.location.reload()
        }}
      >
        刷新页面
      </div>
      <div className=" text-lg" onClick={() => {}}>
        本地调试
        <div className="flex w-full  items-center space-x-2">
          <Input
            type="text"
            className="w-[600px] h-[60px]"
            onChange={e => {
              setLocalHref(e.target.value)
            }}
          />
          <Button
            onClick={() => {
              window.location.href = localHref
            }}
          >
            go
          </Button>
        </div>
      </div>
    </>
  )
}
export default CameraTest
