import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/shad/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/shad/alert-dialog'
import { Button } from '@/components/ui/shad/button'
import { useState } from 'react'
import { useToast } from '@/components/ui/shad/use-toast'
import { useDebounce } from '@/hooks/useDebounce'
import { ThreeView } from '@/components/pages/3d/ThreeView'
import { MyContainer } from '@/components/ui/MyContainer'

function TestComponent() {
  // const [openModal, setOpenModal] = useState(false)
  // const [openErrModal, setOpenErrModal] = useState(false)
  // const [payModal, setPayModal] = useState(false)
  // const [uploadModal, setUploadModal] = useState(false)
  const { toast } = useToast()
  const [createLoading, setCreateLoading] = useState(false)

  const handleDebounceFn = useDebounce(async () => {
    try {
      setCreateLoading(true)

      await fetch(
        'https://data.flutterweb.cn/mock/11/one-graph-wujiebantu/graphql/mockDebounce'
      )
    } catch (error) {
      console.log('error', error)
    } finally {
      setCreateLoading(false)
    }
    // tip: 接口参数等才能作为依赖项
    // loading不能加到依赖项目，不然防抖失效
  }, [])

  return (
    <MyContainer>
      <DemoModule title="loading锁+按钮防抖">
        <DemoItem title="">
          <DemoSingle>
            <div>loading按钮锁住，然后按钮防抖</div>
            <Button
              variant="default"
              size="default"
              onClick={() => {
                handleDebounceFn()
              }}
              loading={createLoading}
            >
              模拟快速点击
            </Button>
          </DemoSingle>
        </DemoItem>
      </DemoModule>
      <DemoModule title="toast">
        <DemoItem title="toast提示">
          <DemoSingle>
            <Button
              variant="default"
              size="default"
              onClick={() => {
                toast({
                  description: '生成订单失败',
                })
              }}
            >
              toast 默认2s
            </Button>
          </DemoSingle>
          <DemoSingle>
            <Button
              variant="default"
              size="default"
              onClick={() => {
                toast({
                  duration: 500000,
                  description:
                    '生成订单失败，请重试生成订单失败，请重试生成订单失败，请重试',
                })
              }}
            >
              toast 自定义时间
            </Button>
          </DemoSingle>
        </DemoItem>
      </DemoModule>
      <DemoModule title="按钮">
        <DemoItem title="style 样式">
          <DemoSingle>
            <Button variant="default">default</Button>
          </DemoSingle>
          <DemoSingle>
            <Button variant="outline">outline</Button>
          </DemoSingle>
          <DemoSingle>
            <Button variant="minor">重新上传</Button>
          </DemoSingle>
        </DemoItem>

        <DemoItem title="size 尺寸">
          <DemoSingle>
            <Button variant="default" size="default">
              尺寸 default
            </Button>
          </DemoSingle>
          <DemoSingle>
            <Button variant="default" size="md">
              尺寸 md
            </Button>
          </DemoSingle>
          <DemoSingle>
            <Button variant="default" size="lg">
              尺寸 lg
            </Button>
          </DemoSingle>
        </DemoItem>
        <DemoItem title="loading">
          <DemoSingle>
            <Button loading>尺寸 default</Button>
          </DemoSingle>
          <DemoSingle>
            <Button variant="default" size="md" loading>
              尺寸 md
            </Button>
          </DemoSingle>
          <DemoSingle>
            <Button variant="default" size="lg" loading>
              <div>尺寸 lg</div>
            </Button>
          </DemoSingle>
        </DemoItem>
      </DemoModule>
      <DemoModule title="3d渲染">
        <DemoSingle>
          <div className="w-[320px] h-[320px]">
            <ThreeView
              url="https://webcdn.wujieai.com/images/tripo_draft_eead89f1-242c-4b58-9968-6538e16cc024.glb"
              onComplete={() => {
                console.log('complete')
              }}
            />
          </div>
        </DemoSingle>
      </DemoModule>
      {/* <DemoModule title="弹窗-（点蒙层可关闭）">
        <DemoItem title="通知弹窗">
          <DemoSingle>
            <Dialog open={openModal} onOpenChange={setOpenModal}>
              <DialogTrigger asChild>
                <Button variant="default">生成出错</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>生成出错</DialogTitle>
                </DialogHeader>
                <DialogDescription>
                  您确认不需要打印，
                  <br />
                  直接结束流程并返回首页吗？
                </DialogDescription>

                <DialogFooter>
                  <Button
                    variant="outline"
                    size="lg"
                    className=" w-[256px] mr-6"
                    onClick={() => setOpenModal(false)}
                  >
                    取消
                  </Button>
                  <Button variant="default" size="lg" className=" w-[256px] ">
                    重新生成
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </DemoSingle>
          <DemoSingle>
            <Dialog open={openErrModal} onOpenChange={setOpenErrModal}>
              <DialogTrigger asChild>
                <Button variant="default">机器出现问题</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>机器出现问题</DialogTitle>
                </DialogHeader>
                <DialogDescription>
                  因您长时间未操作
                  <br />
                  系统将在 60s 后自动返回首页
                  <div className="text-[#F26B8C]">
                    请注意：返回首页后所有操作记录都将丢失
                  </div>
                </DialogDescription>

                <DialogFooter>
                  <Button
                    variant="default"
                    size="lg"
                    className=" w-[256px] "
                    onClick={() => setOpenErrModal(false)}
                  >
                    知道了
                  </Button>
                </DialogFooter>

                <div className="text-center mt-12">60s 后自动返回首页</div>
              </DialogContent>
            </Dialog>
          </DemoSingle>
        </DemoItem>

        <DemoItem title="支付弹窗">
          <DemoSingle>
            <Dialog open={payModal} onOpenChange={setPayModal}>
              <DialogTrigger asChild>
                <Button variant="default">支付弹窗</Button>
              </DialogTrigger>
              <DialogContent className="w-[1074px]">
                <DialogHeader>
                  <DialogTitle>使用微信扫描二维码上传图片</DialogTitle>
                </DialogHeader>
                <DialogDescription>
                  <QRCode
                    value={'https://www.baidu.com'}
                    size={396}
                    fgColor="#000"
                  />
                </DialogDescription>
                <div className="absolute left-[50%] translate-x-[-50%]  -bottom-[100px]">
                  <Button
                    variant="outline"
                    size="md"
                    className=" w-[256px] mr-6 text-neutral-900"
                    onClick={() => setPayModal(false)}
                  >
                    取消支付
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </DemoSingle>
          <DemoSingle>
            <Dialog open={uploadModal} onOpenChange={setUploadModal}>
              <DialogTrigger asChild>
                <Button variant="default">取消上传弹窗</Button>
              </DialogTrigger>
              <DialogContent className="w-[608px]">
                <DialogHeader>
                  <DialogTitle>使用微信扫描二维码上传图片</DialogTitle>
                </DialogHeader>
                <DialogDescription>
                  <QRCode
                    value={'https://www.baidu.com'}
                    size={396}
                    fgColor="#000"
                  />
                </DialogDescription>
                <div className="absolute left-[50%] translate-x-[-50%]  -bottom-[100px]">
                  <Button
                    variant="outline"
                    size="md"
                    className=" w-[256px] mr-6 text-neutral-900"
                    onClick={() => setUploadModal(false)}
                  >
                    取消上传
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
          </DemoSingle>
        </DemoItem>
      </DemoModule> */}
      <DemoModule title="alter-弹窗（点蒙层不可关闭）">
        <DemoItem title="通知弹窗">
          <DemoSingle>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="default">Show Dialog</Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    your account and remove your data from our servers.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction>Continue</AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DemoSingle>
        </DemoItem>
      </DemoModule>
    </MyContainer>
  )
}

const DemoModule = ({
  title,
  children,
}: {
  title: string
  children: React.ReactNode
}) => {
  return (
    <div className="p-2">
      <div className="text-xl font-bold mb-2">{title}</div>
      {children}
    </div>
  )
}

const DemoItem = ({
  title,
  children,
}: {
  title: string
  children: React.ReactNode
}) => {
  return (
    <div>
      <div>{title}</div>
      <div className="flex items-center">{children}</div>
    </div>
  )
}

const DemoSingle = ({ children }: { children: React.ReactNode }) => {
  return <div className="p-4">{children}</div>
}

export default TestComponent
