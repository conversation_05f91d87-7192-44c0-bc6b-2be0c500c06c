import { createBrowserRouter } from 'react-router-dom'
import { Layout } from '@/components/layouts/default'
import ErrorBoundary from '@/components/business/ErrorBoundary'
import SourceLoadStatus from '@/components/business/SourceLoadStatus'
import TestComponent from '@/pages/test'
import Index from '@/pages/index'
import Photo from '@/pages/photo'
import Result from '@/pages/result'
import Print from '@/pages/print'
import Silent from '@/pages/silent'
import Detail from '@/pages/detail'
import CameraTest from '@/pages/test/CameraTest'
import DownloadImg from '@/pages/result/download'
import EventPage from '@/pages/event'
import PaymentSuccess from '@/pages/payment/success'
import PaymentFail from '@/pages/payment/fail'
import ThemeDetail from '@/pages/theme/detail'

import Preview from '@/pages/preview'
import Video from '@/pages/video'
import { VideoLayout } from '@/components/pages/video/videoLayout'

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <EventPage />,
      },
      {
        path: 'home',
        element: <Index />,
      },
      {
        path: 'silent',
        element: <Silent />,
      },
      {
        path: 'theme',
        element: <ThemeDetail />,
      },
      {
        path: 'photo',
        element: <Photo />,
      },
      {
        path: 'result',
        element: <Result />,
      },
      {
        path: 'detail',
        element: <Detail />,
      },
      {
        path: 'print',
        element: <Print />,
      },
    ],
  },
  {
    path: 'download',
    element: <DownloadImg />,
  },
  {
    path: 'preview',
    element: <Preview />,
  },
  {
    path: '/video',
    element: <VideoLayout />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <Video />,
      },
    ],
  },
  {
    path: '/test',
    children: [
      {
        path: 'component',
        element: <TestComponent />,
      },
      {
        path: 'camera',
        element: <CameraTest />,
      },
    ],
  },
  {
    path: '/payment',
    children: [
      {
        path: 'success',
        element: <PaymentSuccess />,
      },
      {
        path: 'fail',
        element: <PaymentFail />,
      },
    ],
  },

  {
    path: '*',
    element: (
      <SourceLoadStatus
        loadStatus="failed"
        errorText="页面不存在，请重新加载"
      />
    ),
  },
])
