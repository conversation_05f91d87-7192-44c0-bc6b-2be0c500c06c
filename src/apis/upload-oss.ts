import * as qiniu from 'qiniu-js'
import { UploadProgress } from 'qiniu-js/esm/upload'
import {
  OssBucket,
  QiniuTokenDocument,
  QiniuTokenQuery,
  QiniuTokenQueryVariables,
} from '@/graphqls/types'
import { CompressorOptionsProps, compressorImage } from '@/utils'
import { client } from '@/graphqls/apollo/client'

export const getQiNiuToken = (
  /** 原始文件名，包含后缀。作为后端类型判断的依据 */
  originalFileName: string,
  /** 文件资源名，为 null 或者 undefined 时则自动使用文件的 hash 作为文件名*/
  key: string | undefined,
  bucket: OssBucket,
  dirName?: string
) => {
  return client.query<QiniuTokenQuery, QiniuTokenQueryVariables>({
    query: QiniuTokenDocument,
    variables: {
      originalFileName,
      key,
      bucket,
      dirName,
    },
    fetchPolicy: 'network-only',
  })
}

interface UploadOptionsProps {
  compressOptions?: CompressorOptionsProps
  isCompress?: boolean
  onProgress?: (val: UploadProgress) => void
}

/**
 * @doc
 * https://developer.qiniu.com/kodo/1283/javascript
 * @param file
 * @param key 文件名为 undefined，自动用hash作为文件名
 * @param dirname 文件存储路径
 * @param bucket 默认值TRADE_PROTECT（开启原图保护），作用于作品
 * @returns
 */
export const uploadQiNiu = async (
  file: File,
  key: string | undefined,
  bucket: OssBucket = OssBucket.TRADE_PROTECT,
  dirname: string | undefined = undefined,
  options?: UploadOptionsProps
) => {
  const res = await getQiNiuToken(file?.name, key, bucket, dirname)
  const token = res?.data?.upToken?.token || ''
  const QNUPLOAD = res?.data?.upToken?.region
    ? `upload-${res.data.upToken.region}.qiniup.com`
    : `upload-z0.qiniup.com`

  const newFile =
    (options?.isCompress ?? true)
      ? ((await compressorImage(file, options?.compressOptions)) as File)
      : file

  console.log(options?.isCompress ?? true)

  const observable = qiniu.upload(newFile, key, token, undefined, {
    uphost: QNUPLOAD,
  })

  const p = new Promise((resolve, reject) => {
    observable.subscribe({
      next: options?.onProgress,
      error(err) {
        reject(err)
      },
      complete(res) {
        resolve(res)
      },
    })
  })

  const uploadRes: any = await p

  return {
    url: uploadRes.baseUrl + '/' + uploadRes.key,
  }
}
