// 招聘边框详情
export type FrameDetail = {
  id: number
  url: string
}
// 活动详情
export type EventItem = {
  id: number
  name: string
  theme_id: number
  layout_direction: string
  cover_image_url: string
  available_points: number
  ui_style: string
  desc: string
  created_at: number
  user_id: number
  user_name: string
  theme_name: string
  event_frames: FrameDetail[]
  selected_frame: FrameDetail
  enable_download: boolean
  enable_evaluation: boolean
  enable_mail: boolean
  enable_print: boolean
  enable_sms: boolean
  enable_video: boolean
  enable_pay: 0 | 1 // 0: 不需要支付 1: 需要支付
  generations_per_model: number
  models_per_generation: number
  start_time: number
  stop_time: number
  event_themes: ThemeDetail[]
  tags: TagDetail[]
}
// 虚拟设备信息
export type VirtualInfo = {
  id: number
  uid: string
  user_id: number
  company_id: number
  event_id: number
  device_id: number
  device_uid: string
  device_token: string
  expire_ts: number
  status: number
}
// 主题详情
export type ThemeDetail = {
  cover_image: string
  cover_image_female: string
  id: number
  name: string
  template_count: number
  type: PaySettingEnum
  price: number
  row?: number
  female_model_count?: number
  male_model_count?: number
  video: {
    resultUrl: string
  }
}
// tag 详情
export type TagDetail = {
  id: number
  name: string
  themes: ThemeDetail[]
}

export type ThemeList = {
  data: {
    data: ThemeDetail[]
  }
}

// 支付设置
export enum PaySettingEnum {
  /** 不需要支付 */
  NONE = 0,
  /** 需要支付 */
  NEED_PAY = 1,
}

// 支付结果
export type OrderDetail = {
  amount: string
  company_id: number
  count: number
  coupon_id: number
  created_at: number
  currency: 'USD'
  device_id: number
  device_name: string
  device_uid: string
  event_id: number
  event_name: string
  goods_id: number
  order_id: number
  origin_amount: string
  pay_id: string
  status: OrderStatusEnum
  status_name: string
  updated_at: number
}

// 支付状态
export enum OrderStatusEnum {
  /** 正在处理中 */
  PROCESSING = 1,
  /** 已支付 */
  PAID = 3,
  /** 已过期 */
  EXPIRED = 4,
  /** 支付失败 */
  FAILED = 5,
}

// 打印状态
export enum PrintStatusEnum {
  /** 等待打印 */
  PENDING = 0,
  /** 打印成功 */
  SUCCESS = 1,
  /** 打印失败 */
  FAILED = 2,
  /** 打印中 */
  PRINTING = 3,
}

// 视频 shorts 生成状态
export enum ShortsStatusEnum {
  PENDING_QUEUE = 'PENDING_QUEUE',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}
