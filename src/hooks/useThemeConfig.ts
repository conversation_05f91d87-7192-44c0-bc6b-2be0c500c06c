import { themeConfigAtom } from '@/stores'
import { ThemeConfigProps } from '@/stores/types'
import { hexToRgb, toCDNImage } from '@/utils'
import { useAtom } from 'jotai'
import Cookies from 'js-cookie'
import { useSearchParams } from 'react-router-dom'

/** 自定义主题 */
export const useThemeConfig = () => {
  const [params] = useSearchParams()
  const [themeConfigA, setThemeConfigA] = useAtom(themeConfigAtom)

  const setThemeConfig = (themeConfig: ThemeConfigProps) => {
    Cookies.set('themeConfig', JSON.stringify(themeConfig))
    setThemeConfigA(themeConfig)
  }

  const themeConfig =
    themeConfigA ||
    (JSON.parse(Cookies.get('themeConfig') || '{}') as ThemeConfigProps)

  /** 用于预览页面的主题配置 */
  const getPreviewThemeConfig = () => {
    const bgColor = params.get('bgColor')
    // 横版背景图
    const bgUrl = params.get('bgUrl')
    // 竖版背景图
    const portraitBgUrl = params.get('verticalBgUrl')
    const textColor = params.get('textColor')
    const themeColor = params.get('themeColor')
    const buttonTextColor = params.get('buttonTextColor')
    const logoUrl = params.get('logoUrl')

    return {
      bgColor,
      bgUrl,
      textColor,
      themeColor,
      buttonTextColor,
      logoUrl,
      portraitBgUrl,
    }
  }

  const getBgVal = (themeConfig: ThemeConfigProps, isPortrait?: boolean) => {
    let backgroundImage
    let backgroundColor
    if (themeConfig.bgUrl || themeConfig.portraitBgUrl) {
      backgroundImage = `url(${
        isPortrait ? themeConfig.portraitBgUrl : themeConfig.bgUrl
      })`
    } else if (themeConfig.bgColor) {
      backgroundColor = themeConfig.bgColor
    } else {
      backgroundImage = `url(${toCDNImage(
        isPortrait ? '/images/common/bg-v.png' : '/images/common/bg.png'
      )})`
    }
    return { backgroundColor, backgroundImage }
  }

  const getBgStyle = (themeConfig: ThemeConfigProps, isPortrait?: boolean) => {
    const { backgroundColor, backgroundImage } = getBgVal(
      themeConfig,
      isPortrait
    )

    if (backgroundImage) return { backgroundImage }
    if (backgroundColor)
      return {
        background: backgroundColor,
      }
  }

  const setBodyBgStyle = (
    themeConfig: ThemeConfigProps,
    isPortrait?: boolean
  ) => {
    const { backgroundColor, backgroundImage } = getBgVal(
      themeConfig,
      isPortrait
    )
    if (backgroundImage) document.body.style.backgroundImage = backgroundImage
    if (backgroundColor) document.body.style.backgroundColor = backgroundColor
  }

  const getDynamicStyles = (themeConfig: ThemeConfigProps) => {
    let dynamicStyles = ``

    if (themeConfig.themeColor) {
      dynamicStyles += `
        .text-gradient-primary {
          color: ${themeConfig?.themeColor};
          background: none;
        }
        .bg-gradient-primary {
          background: ${themeConfig?.themeColor};
        }
        .border-primary {
          border-color: ${themeConfig?.themeColor};
        }
        .border-light-primary {
          border-color: rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b},
          0.16);
        }
        .border-primary {
          border-color: ${themeConfig?.themeColor};
        }
        .text-primary {
          color: ${themeConfig?.themeColor};
        }
        .shadow-button-primary {
          box-shadow: 0px 76px 52px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.2),
          0px 20px 28px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.36);
        }
        .shadow-box-primary {
          box-shadow: 0px 11px 25px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.1),
          0px 45px 45px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.09),
          0px 102px 61px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.05),
          0px 181px 72px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.01),
          0px 282px 79px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r},
          ${hexToRgb(themeConfig?.themeColor || '')?.g},
          ${hexToRgb(themeConfig?.themeColor || '')?.b},
          0);
        }
        .shadow-box-small-primary {
          box-shadow: 0px 8px 18px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.1),
          0px 32px 32px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.09),
          0px 72px 44px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.05),
          0px 130px 51px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.g}, 
          ${hexToRgb(themeConfig?.themeColor || '')?.b}, 
          0.01),
          0px 200px 56px rgba(
          ${hexToRgb(themeConfig?.themeColor || '')?.r},
          ${hexToRgb(themeConfig?.themeColor || '')?.g},
          ${hexToRgb(themeConfig?.themeColor || '')?.b},
          0);
        }
      `
    }

    if (themeConfig.buttonTextColor) {
      dynamicStyles += `
        .btn-text-color {
          color: ${themeConfig.buttonTextColor};
        }
      `
    }

    if (themeConfig.textColor) {
      dynamicStyles += `
        .bg-text-color {
          color: ${themeConfig.textColor};
        }
      `
    }

    return dynamicStyles
  }

  const injectGlobalStyles = (themeConfig: ThemeConfigProps) => {
    const styles = getDynamicStyles(themeConfig)

    const styleSheet = document.createElement('style')
    styleSheet.id = 'dynamic-styles'
    styleSheet.innerText = styles.replace(/\n/g, '')
    document.head.appendChild(styleSheet)
  }

  const removeGlobalStyles = () => {
    const styleSheet = document.getElementById('dynamic-styles')
    if (styleSheet) {
      document.head.removeChild(styleSheet)
    }
  }

  return {
    setThemeConfig,
    themeConfig,
    getPreviewThemeConfig,
    getBgStyle,
    injectGlobalStyles,
    removeGlobalStyles,
    /** 设置body背景样式 */
    setBodyBgStyle,
  }
}
