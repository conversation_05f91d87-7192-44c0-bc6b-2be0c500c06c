/**
 *
 * 校验机器状态
 */

import { useAtom } from 'jotai'
import { cameraEnableAtom, machineInfoAtom, printerEnableAtom } from '@/stores'
import { CameraStatus, PrinterStatus } from '@/stores/types'
import { toast } from '@/components/ui/shad/use-toast'
import { isMirror } from '@/utils'
import { useTranslation } from 'react-i18next'

export const useValidateDevice = () => {
  const [cameraEnable] = useAtom(cameraEnableAtom)
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const { t } = useTranslation()

  const validateCamera = () => {
    /** 只有在启用摄像头并且不可用的时候需要处理 */
    if (
      cameraEnable &&
      isMirror() &&
      machineInfo.cameraStatus !== CameraStatus.AVAILABLE
    ) {
      toast({
        description: t('摄像头未识别到，请稍后再试或联系工作人员'),
      })
      return false
    }

    return true
  }

  const validatePrinter = () => {
    console.log('valid-machineInfo', machineInfo)
    /** 只有在启用打印机并且不可用的时候需要处理 */
    if (
      printerEnable &&
      isMirror() &&
      machineInfo?.printers?.every(
        it => it.printerStatus !== PrinterStatus.AVAILABLE
      )
    ) {
      toast({
        description: t('打印机未识别到，请稍后再试或联系工作人员'),
      })
      return false
    }

    if (
      printerEnable &&
      isMirror() &&
      machineInfo?.printers?.every(it => it.printerPaperNum < 1)
    ) {
      toast({
        description: t('打印机纸张不足，请联系工作人员'),
      })
      return false
    }
    return true
  }

  return {
    /** 摄像头异常校验 */
    validateCamera,
    /** 打印机异常校验 */
    validatePrinter,
  }
}
