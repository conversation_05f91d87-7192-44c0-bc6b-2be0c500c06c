import { OssBucket, PrintTemplateLocationInput } from '@/graphqls/types'
import { useRef, useState } from 'react'
import ImageJS from 'image-js'
import { uploadQiNiu } from '@/apis/upload-oss'

/**
 * 模版拼接
 */
interface TemplteProps {
  template: {
    templateWidth: number
    templateHeight: number
    bgUrl?: string
    textureUrl?: string
    location?: PrintTemplateLocationInput[]
  }
  imgUrls?: string[]
}
/**
 * 模拟object-fit:cover
 * 将源和目标的中心点对齐，超出剪裁
 */
function computeImageDrawArea({
  targetW,
  targetH,
  targetCenterX,
  targetCenterY,
  sourceW,
  sourceH,
}: {
  targetW: number
  targetH: number
  targetCenterX: number
  targetCenterY: number
  sourceW: number
  sourceH: number
}) {
  const targetX = targetCenterX - targetW / 2
  const targetY = targetCenterY - targetH / 2

  let sx = 0
  let sy = 0
  let sw = sourceW
  let sh = sourceH

  let testWidthOk = false
  let testHeightOk = false
  let gapW = 0
  let gapH = 0
  const wRatio = targetW / sourceW
  const hRatio = targetH / sourceH

  // 尝试匹配宽度
  // case1 原图 宽度匹配后，高度 > 目标区域
  const n_sourceH = sourceH * wRatio
  if (n_sourceH >= targetH) {
    testWidthOk = true
    gapH = n_sourceH - targetH
  }

  // 尝试匹配高度
  // case2 原图 高度匹配后，宽度 > 目标区域
  const n_sourceW = sourceW * hRatio
  if (n_sourceW >= targetW) {
    testHeightOk = true
    gapW = n_sourceW - targetW
  }

  let matchWidth = false
  if (testWidthOk) {
    matchWidth = true
  } else if (testHeightOk) {
    matchWidth = false
  }

  if (matchWidth) {
    // 匹配宽度，剪裁高度
    sy = gapH / 2 / wRatio
    sh = targetH / wRatio
  } else {
    // 匹配高度，剪裁宽度
    sx = gapW / 2 / hRatio
    sw = targetW / hRatio
  }

  // console.log('----')
  // console.log('原图', sourceW, sourceH)
  // console.log(`${sx}-${sw}, ${sy}-${sh}`)
  // console.log('目标', targetW, targetH)

  return [sx, sy, sw, sh, targetX, targetY, targetW, targetH]
}

export const useTemplateSplicing = () => {
  const loading = useRef(false)
  const imageSplicingResult = async ({ template, imgUrls }: TemplteProps) => {
    try {
      loading.current = true
      const $canvas = document.createElement('canvas') as HTMLCanvasElement
      $canvas.width = template.templateWidth
      $canvas.height = template.templateHeight
      const ctx = $canvas.getContext('2d')!

      const imgArray: Array<{
        img: HTMLImageElement
        w: number
        h: number
        centerX: number
        centerY: number
        rotate: number
        fit: boolean
      }> = []

      if (template.bgUrl) {
        const $bottom = new Image()
        $bottom.crossOrigin = 'Anonymous'
        $bottom.src = template.bgUrl
        imgArray.push({
          img: $bottom,
          w: template.templateWidth,
          h: template.templateHeight,
          centerX: template.templateWidth / 2,
          centerY: template.templateHeight / 2,
          rotate: 0,
          fit: false,
        })
      }

      template.location?.forEach((it, index) => {
        if (imgUrls?.[index]) {
          const $img = new Image()
          $img.crossOrigin = 'Anonymous'
          $img.src = imgUrls[index]
          imgArray.push({
            img: $img,
            w: it.width!,
            h: it.height!,
            centerX: it.x!,
            centerY: it.y!,
            rotate: (it.rotate! * Math.PI) / 180,
            fit: true,
          })
        }
      })

      if (template.textureUrl) {
        const $top = new Image()
        $top.crossOrigin = 'Anonymous'
        $top.src = template.textureUrl
        imgArray.push({
          img: $top,
          w: template.templateWidth,
          h: template.templateHeight,
          centerX: template.templateWidth / 2,
          centerY: template.templateHeight / 2,
          rotate: 0,
          fit: false,
        })
      }

      await Promise.all(
        imgArray.map(it => new Promise(resolve => (it.img.onload = resolve)))
      )

      // const dpr = window.devicePixelRatio || 1
      // ctx.scale(dpr, dpr)
      ctx.clearRect(0, 0, template.templateWidth, template.templateWidth)
      imgArray.forEach(it => {
        ctx.save()
        // rotate
        ctx.translate(it.centerX, it.centerY)
        ctx.rotate(it.rotate)
        ctx.translate(-it.centerX, -it.centerY)
        // draw

        // draw
        if (it.fit) {
          ctx.drawImage(
            it.img,
            // @ts-expect-error 扩张参数必须具有元组类型或传递给 rest 参数
            ...computeImageDrawArea({
              targetW: it.w,
              targetH: it.h,
              targetCenterX: it.centerX,
              targetCenterY: it.centerY,
              sourceW: it.img.naturalWidth,
              sourceH: it.img.naturalHeight,
            })
          )
        } else {
          ctx.drawImage(it.img, 0, 0, it.w, it.h, 0, 0, it.w, it.h)
        }
        ctx.restore()
      })

      ctx.save()

      const image = ImageJS.fromCanvas($canvas)
      const newImage = image.crop({
        x: 0,
        y: 0,
        width: template.templateWidth,
        height: template.templateHeight,
      })
      const blob = await newImage.toBlob()

      const imgFile = new File([blob!], 'splicingPrint.png', {
        type: blob?.type,
      })

      const { url } = await uploadQiNiu(
        imgFile,
        undefined,
        OssBucket.TRADE_PROTECT,
        'splicingPrint'
      )
      loading.current = false
      return url
    } catch (error) {
      loading.current = false
      console.log(error, 'error')
    }
  }

  return {
    imageSplicingResult,
    loading,
  }
}
