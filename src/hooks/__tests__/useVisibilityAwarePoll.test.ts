import { renderHook, act } from '@testing-library/react'
import { useVisibilityAwarePoll } from '../useVisibilityAwarePoll'

// Mock document.addEventListener and document.removeEventListener
const mockAddEventListener = jest.fn()
const mockRemoveEventListener = jest.fn()

Object.defineProperty(document, 'addEventListener', {
  value: mockAddEventListener,
})

Object.defineProperty(document, 'removeEventListener', {
  value: mockRemoveEventListener,
})

// Mock document.hidden
Object.defineProperty(document, 'hidden', {
  writable: true,
  value: false,
})

describe('useVisibilityAwarePoll', () => {
  let mockPollFunction: jest.Mock
  let mockStopFunction: jest.Mock

  beforeEach(() => {
    mockPollFunction = jest.fn()
    mockStopFunction = jest.fn()
    mockAddEventListener.mockClear()
    mockRemoveEventListener.mockClear()
    jest.clearAllTimers()
    jest.useFakeTimers()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('should start polling on mount', () => {
    renderHook(() => useVisibilityAwarePoll(mockPollFunction, mockStopFunction))

    expect(mockPollFunction).toHaveBeenCalledTimes(1)
    expect(mockAddEventListener).toHaveBeenCalledWith(
      'visibilitychange',
      expect.any(Function)
    )
  })

  it('should stop polling when page becomes hidden', () => {
    const { result } = renderHook(() =>
      useVisibilityAwarePoll(mockPollFunction, mockStopFunction)
    )

    // Get the visibility change handler
    const visibilityChangeHandler = mockAddEventListener.mock.calls[0][1]

    // Simulate page becoming hidden
    Object.defineProperty(document, 'hidden', { value: true })
    act(() => {
      visibilityChangeHandler()
    })

    expect(mockStopFunction).toHaveBeenCalled()
  })

  it('should resume polling when page becomes visible', () => {
    const { result } = renderHook(() =>
      useVisibilityAwarePoll(mockPollFunction, mockStopFunction)
    )

    // Get the visibility change handler
    const visibilityChangeHandler = mockAddEventListener.mock.calls[0][1]

    // First make page hidden
    Object.defineProperty(document, 'hidden', { value: true })
    act(() => {
      visibilityChangeHandler()
    })

    // Clear the mock to count new calls
    mockPollFunction.mockClear()

    // Then make page visible again
    Object.defineProperty(document, 'hidden', { value: false })
    act(() => {
      visibilityChangeHandler()
    })

    // Fast-forward the setTimeout
    act(() => {
      jest.advanceTimersByTime(100)
    })

    expect(mockPollFunction).toHaveBeenCalled()
  })

  it('should clean up event listeners on unmount', () => {
    const { unmount } = renderHook(() =>
      useVisibilityAwarePoll(mockPollFunction, mockStopFunction)
    )

    unmount()

    expect(mockStopFunction).toHaveBeenCalled()
    expect(mockRemoveEventListener).toHaveBeenCalledWith(
      'visibilitychange',
      expect.any(Function)
    )
  })

  it('should provide control methods', () => {
    const { result } = renderHook(() =>
      useVisibilityAwarePoll(mockPollFunction, mockStopFunction)
    )

    expect(result.current).toHaveProperty('startPoll')
    expect(result.current).toHaveProperty('stopPoll')
    expect(result.current).toHaveProperty('resetPollStatus')
    expect(typeof result.current.startPoll).toBe('function')
    expect(typeof result.current.stopPoll).toBe('function')
    expect(typeof result.current.resetPollStatus).toBe('function')
  })
})
