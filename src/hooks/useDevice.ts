/**
 * 机器相关
 */

import {
  DeviceInfoDocument,
  DeviceInfoQuery,
  DeviceType,
  MultiScreenDisplayEnum,
  useDeviceReportMutation,
  Direction,
} from '@/graphqls/types'
import { useImperativeQuery } from './useImperativeQuery'
import { useAtom, useSetAtom } from 'jotai'
import {
  cameraEnableAtom,
  deviceTypeAtom,
  isSplitPrintingAtom,
  machineInfoAtom,
  multiScreenDisplayAtom,
  phoneAtom,
  printerEnableAtom,
  screenOrientationAtom,
  isPortraitCameraEnableAtom,
  selectedEventIdAtom,
  selectedEventDetailAtom,
  selectedImageFrameAtom,
} from '@/stores'
import { useTranslation } from 'react-i18next'
import { useBridge } from './useBridge'
import Cookies from 'js-cookie'
import { CameraStatus, MachineStatus, PrinterStatus } from '@/stores/types'
import {
  DEVICE_TOKEN,
  DEVICE_TYPE,
  VIRTUAL_UID,
  VIRTUAL_UID_TMP,
} from '@/configs'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { isMirror } from '@/utils'
import { toast } from '@/components/ui/shad/use-toast'
import _ajax from '@/utils/ajax'
import _api from '@/apis/maze.api'

export const useDevice = () => {
  const setScreenOrientation = useSetAtom(screenOrientationAtom)

  const [, setCameraEnable] = useAtom(cameraEnableAtom)
  const [, setIsPortraitCameraEnable] = useAtom(isPortraitCameraEnableAtom)
  const [, setPrinterEnable] = useAtom(printerEnableAtom)
  const [, setIsSplitPrinting] = useAtom(isSplitPrintingAtom)
  const [, setPhone] = useAtom(phoneAtom)
  const [, setMachineInfo] = useAtom(machineInfoAtom)
  const [, setDeviceType] = useAtom(deviceTypeAtom)
  const [, setMultiScreenDisplay] = useAtom(multiScreenDisplayAtom)
  const [, setSelectedEventId] = useAtom(selectedEventIdAtom)
  const [, setSelectedEventDetail] = useAtom(selectedEventDetailAtom)
  const [, setSelectedFrame] = useAtom(selectedImageFrameAtom)

  const { machineStatus, appPreloadResource } = useBridge()
  const [searchParam] = useSearchParams()
  const navigate = useNavigate()
  const { t } = useTranslation()

  const deviceInfoQuery =
    useImperativeQuery<DeviceInfoQuery>(DeviceInfoDocument)

  const [deviceReportMutation] = useDeviceReportMutation()

  /**
   * 只从后端拿是否开启摄像头/照相机的状态，其他信息从jsb里获取
   *  */
  const getDeviceInfo = async () => {
    const { data } = await deviceInfoQuery()

    if (data) {
      setCameraEnable(data.deviceQuery?.deviceInfo?.cameraEnable || false)
      setIsPortraitCameraEnable(
        data.deviceQuery?.deviceInfo?.cameraConfig || false
      )
      setPrinterEnable(data.deviceQuery?.deviceInfo?.printerEnable || false)
      setDeviceType(
        data.deviceQuery?.deviceInfo?.deviceType || DeviceType.MIRROR
      )
      setIsSplitPrinting(data.deviceQuery?.deviceInfo?.splitPrinting || false)

      // 横竖屏
      const isLandScape =
        data.deviceQuery?.deviceInfo?.direction === Direction.CROSSWISE
      setScreenOrientation({
        isLandScape,
        isPortrait: !isLandScape,
      })
      setPhone(data?.deviceQuery?.deviceInfo?.phone)
      setMultiScreenDisplay(
        data?.deviceQuery?.deviceInfo?.multiScreenDisplay ??
          MultiScreenDisplayEnum.SAME
      )
      return data.deviceQuery?.deviceInfo
    }
  }
  const getDefaultDeviceInfo = async () => {
    const { data } = await deviceInfoQuery()
    if (data) {
      return data.deviceQuery?.deviceInfo
    }
  }
  /** 只有照相机环境需要更新机器信息 */
  const updateDeviceInfo = () => {
    console.log('updateDeviceInfo start: ')
    if (isMirror()) {
      console.log('updateDeviceInfo isMirror:', isMirror())
      try {
        machineStatus({
          callback: (res: MachineStatus) => {
            console.log('jsb-获取设备信息', res)
            setMachineInfo(info => ({
              ...info,
              cameraStatus: res.cameraStatus,
              printerStatus: res.printerStatus,
              printerPaperNum: res.printerPaperNum,
              printerInkStatus: res.printerInkStatus,
              networkStatus: res.networkStatus,
              printers: res.printers,
              hasSecondaryScreen: res.hasSecondaryScreen ?? false,
              cameraType: res?.cameraType,
            }))

            /** 机器信息上报 */
            deviceReportMutation({
              variables: {
                param: {
                  cameraAvailable: res.cameraStatus === CameraStatus.AVAILABLE,
                  secondaryScreen: res.hasSecondaryScreen ?? false,
                  printList: res.printers?.map(it => ({
                    printerAvailable:
                      it.printerStatus === PrinterStatus.AVAILABLE,
                    printPaperRemain: it.printerPaperNum,
                    type: it.type,
                  })),
                },
              },
            })
          },
        })
      } catch (error) {
        console.log('updateDeviceInfo error', error)
      }
    }
  }

  const getTokenByUid = async () => {
    const linkVirTualUid = searchParam.get(VIRTUAL_UID)
    const linkVirTualUidTmp = searchParam.get(VIRTUAL_UID_TMP)
    if (!linkVirTualUid && !linkVirTualUidTmp) return
    Cookies.set(DEVICE_TYPE, 'webapp', {
      expires: 365,
    })
    let res
    if (linkVirTualUid) {
      res = await _ajax.get(_api.get_tokens, {
        params: {
          virtual_uid: linkVirTualUid,
        },
      })
    } else if (linkVirTualUidTmp) {
      res = await _ajax.get(_api.get_tokens_tmp, {
        params: {
          // stripe 回调会简单粗暴直接加`?session_id=xxx`，这里也简单粗暴直接获取需要的uid
          virtual_uid: linkVirTualUidTmp?.split('?')?.[0],
        },
      })
    }
    const data = res!.data?.data
    if (res!.data?.code === 200) {
      if (
        data?.device_token &&
        data?.device_token !== Cookies.get(DEVICE_TOKEN)
      ) {
        Cookies.set(DEVICE_TOKEN, data?.device_token, {
          expires: 365,
        })
      }
      if (data?.user_token && data?.user_token !== Cookies.get('token')) {
        Cookies.set('token', data?.user_token, {
          expires: 365,
        })
      }
      if (data?.event_id) {
        setEventParam(data?.event_id)
      }
    } else {
      toast({
        description: t('链接已过期'),
      })
    }
  }

  const setEventParam = async (event_id: number) => {
    const res = await _ajax.get(_api.event_list)
    const data = res?.data?.data?.data
    const curData = data.find((it: any) => it.id === event_id)
    if (curData) {
      const frameDetail = curData?.selected_frame
      if (frameDetail) {
        setSelectedFrame(frameDetail)
      }
      setSelectedEventId(event_id)
      setSelectedEventDetail(curData)
      const nav = searchParam.get('navigate')
      if (!nav) {
        navigate('/')
      }
    }
  }

  /** 针对大屏,需要将deviceToken拼在网址后的场景 */
  const injectDeviceToken = () => {
    const linkDeviceToken = searchParam.get(DEVICE_TOKEN)
    const linkMazeToken = searchParam.get('token') // maze token
    if (linkDeviceToken && linkDeviceToken !== Cookies.get(DEVICE_TOKEN)) {
      Cookies.set(DEVICE_TOKEN, linkDeviceToken, {
        expires: 365,
      })
    }
    if (linkMazeToken && linkMazeToken !== Cookies.get('token')) {
      Cookies.set('token', linkMazeToken, {
        expires: 365,
      })
    }

    if (!linkDeviceToken && !Cookies.get(DEVICE_TOKEN)) {
      throw new Error('device token is required')
    }
  }

  const toAppPreloadResource = (urlList: string[]) => {
    return new Promise((resolve: (res: boolean) => void) => {
      appPreloadResource({
        content: urlList,
        callback: async (cbRes: boolean) => {
          console.log('预加载结束', cbRes)
          resolve(cbRes)
        },
      })
    })
  }

  return {
    /** 从后端获取机器信息 */
    getDeviceInfo,
    /** 更新机器状态 */
    updateDeviceInfo,
    /** 注入Device Token */
    injectDeviceToken,
    getTokenByUid,
    /** app资源预加载 */
    toAppPreloadResource,
    getDefaultDeviceInfo, // 获取机器信息，maze
  }
}
