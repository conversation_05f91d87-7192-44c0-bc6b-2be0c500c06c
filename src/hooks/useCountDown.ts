import { useEffect, useRef, useState } from 'react'

export const useCountDown = (
  _count: number,
  /** 倒计时结束时的回调 */
  onFinish?: () => void
) => {
  const [count, setCount] = useState(_count)
  const timerRef = useRef(0)

  const startSecond = () => {
    clearSecond()
    timerRef.current = window.setInterval(() => {
      setCount(count => {
        if (count > 0) {
          return count - 1
        } else {
          window.clearInterval(timerRef.current)
          return count
        }
      })
    }, 1000)
  }

  const clearSecond = () => {
    window.clearInterval(timerRef.current)
  }

  const resetSecond = (_count: number) => {
    setCount(_count)
    startSecond()
  }

  useEffect(() => {
    if (count === 0) {
      onFinish?.()
    }
  }, [count])

  useEffect(() => {
    return () => {
      clearSecond()
    }
  }, [])

  return {
    second: count,
    /** 开始 */
    startSecond,
    /** 清除定时器 */
    clearSecond,
    /** 重置定时器 */
    resetSecond,
  }
}
