import {
  Direction,
  GetWeappPrintTaskListDocument,
  GetWeappPrintTaskListQuery,
  GetWeappPrintTaskListQueryVariables,
  MirrorPrintTaskFragment,
  PrintTemplateSize,
  useReportPrintSuccessMutation,
  useStartPrintTaskMutation,
} from '@/graphqls/types'
import {
  isSplitPrintingAtom,
  machineInfoAtom,
  printerEnableAtom,
} from '@/stores'
import { useAtom } from 'jotai'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useBridge } from './useBridge'
import { useImperativeQuery } from './useImperativeQuery'
import { PrinterStatus } from '@/stores/types'
import { roateImage } from '@/pages/print'
import { useDevice } from './useDevice'
import { PRINTER_TYPE } from '@/configs'

export const useWeappTask = (size?: PrintTemplateSize) => {
  const [printerEnable] = useAtom(printerEnableAtom)
  const [machineInfo] = useAtom(machineInfoAtom)
  const [isSplitPrinting] = useAtom(isSplitPrintingAtom)

  const { updateDeviceInfo } = useDevice()

  const timerRef = useRef(0)

  /** 小程序提交的task队列 */
  const weappTaskQueue = useRef<MirrorPrintTaskFragment[]>([])

  /** 超时定时器恢复weappPrinting状态 */
  const timeoutRef = useRef<any>(null)

  const { printImage } = useBridge()

  const [reportPrintSuccessAction] = useReportPrintSuccessMutation()
  const [startPrintTaskAction] = useStartPrintTaskMutation()

  const getWeappPrintTaskList = useImperativeQuery<
    GetWeappPrintTaskListQuery,
    GetWeappPrintTaskListQueryVariables
  >(GetWeappPrintTaskListDocument)

  /** 更新hasTask ref引用 */
  const [, forceUpdate] = useState({})

  const hasPrinter = useMemo(() => {
    return (
      printerEnable &&
      (machineInfo?.printers?.some(
        it => it.printerStatus === PrinterStatus.AVAILABLE
      ) ||
        machineInfo?.printerStatus === PrinterStatus.AVAILABLE)
    )
  }, [printerEnable, machineInfo])

  /** 处理小程序的打印任务轮训 */
  useEffect(() => {
    // 小程序有任务或者机器有任务，可以不轮询
    if (weappTaskQueue.current.length > 0 || !hasPrinter || !isSplitPrinting) {
      window.clearInterval(timerRef.current)
      return
    }
    timerRef.current = window.setInterval(() => {
      getWeappPrintTaskList({
        size: size,
      }).then(res => {
        if (res?.data?.mirrorAiTaskQuery?.deviceNextPrintTaskList?.length) {
          weappTaskQueue.current = res?.data?.mirrorAiTaskQuery
            ?.deviceNextPrintTaskList as MirrorPrintTaskFragment[]
          forceUpdate({})
        }
      })
    }, 5 * 1000)

    return () => {
      window.clearInterval(timerRef.current)
    }
  }, [weappTaskQueue.current, hasPrinter, isSplitPrinting, size])

  const reportPrintStatus = async ({
    nextTask,
    taskCacheList,
    cbRes,
  }: {
    nextTask: MirrorPrintTaskFragment
    taskCacheList: MirrorPrintTaskFragment[]
    cbRes?: any
  }) => {
    const orderIds = nextTask?.printImage
      ?.map(it => it?.recordId)
      ?.filter((_, i) => {
        return !!cbRes?.results?.[i]?.success
      })
    await reportPrintSuccessAction({
      variables: {
        printOrderId: nextTask?.orderInfo?.id,
        // 空数组上报表示全失败
        ids: orderIds?.length ? orderIds : [],
      },
    }).catch(() => {})
    //打印完成后，setWeappTaskQueue
    weappTaskQueue.current = taskCacheList
  }

  useEffect(() => {
    // 如果存在机器上的任务，不处理
    if (weappTaskQueue.current.length <= 0 || !hasPrinter || !isSplitPrinting)
      return
    try {
      /** 小程序任务的临时缓存区 */
      const taskCacheList = [...weappTaskQueue.current]
      const nextTask = taskCacheList.shift()
      const direction =
        nextTask?.taskInfo?.templateInfo?.direction ?? Direction.VERTICAL

      // 上报小程序开始打印
      startPrintTaskAction({
        variables: {
          printTaskId: nextTask?.id,
        },
      })

      printImage({
        content: nextTask?.printImage?.map(it => {
          return {
            imageUrl: roateImage(it?.printUrl!, direction),
            // print image会平铺一个图打印多少张的情况，数量全是1
            count: 1,
            type: PRINTER_TYPE[it?.size! as keyof typeof PRINTER_TYPE],
          }
        }),
        callback: async (cbRes: any) => {
          console.log('打印结果', cbRes)
          await reportPrintStatus({
            nextTask: nextTask!,
            taskCacheList,
            cbRes,
          })
          // 成功上报后，清除定时器
          clearTimeout(timeoutRef.current)
          // 小程序打印完成，更新设备
          updateDeviceInfo()
          // dnp打印完成后，不能立即更新打印信息，15s后再上报一次
          setTimeout(() => {
            updateDeviceInfo()
          }, 15 * 1000)
        },
      })

      /** 兜底3分钟打印机不回调callback，能主动上报失败，并且释放weappPrinting */
      timeoutRef.current = setTimeout(
        () => {
          reportPrintStatus({
            nextTask: nextTask!,
            taskCacheList,
          })
        },
        3 * 60 * 1000
      )
    } catch (error) {
      console.log(error, 'error')
    }
    return () => {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
  }, [weappTaskQueue.current, hasPrinter, isSplitPrinting])
}
