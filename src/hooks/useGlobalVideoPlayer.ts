import { useAtom } from 'jotai'
import { globalVideoPlayerAtom, MediaType } from '@/stores'
import { MyMirrorAiTask } from '@/stores/types'

/**
 * 全局媒体播放器控制 hook
 * 提供打开、关闭媒体播放器以及设置播放内容的方法，支持视频和图片
 */
export function useGlobalVideoPlayer() {
  const [globalVideoPlayer, setGlobalVideoPlayer] = useAtom(
    globalVideoPlayerAtom
  )

  /**
   * 打开视频播放器
   * @param video 要播放的视频任务
   * @param poster 海报图片URL
   */
  const openVideoPlayer = (
    video: MyMirrorAiTask,
    poster?: string | null | undefined
  ) => {
    console.log('🎬 GlobalVideoPlayer: 打开全局视频播放器', {
      video: video.id,
      poster,
    })
    setGlobalVideoPlayer({
      open: true,
      mediaType: MediaType.VIDEO,
      video,
      poster,
      imageUrl: undefined,
    })
  }

  /**
   * 打开图片查看器
   * @param imageUrl 要显示的图片URL
   */
  const openImageViewer = (imageUrl: string) => {
    console.log('🖼️ GlobalVideoPlayer: 打开全局图片查看器', { imageUrl })
    setGlobalVideoPlayer({
      open: true,
      mediaType: MediaType.IMAGE,
      imageUrl,
      video: undefined,
      poster: undefined,
    })
  }

  /**
   * 关闭媒体播放器
   */
  const closeVideoPlayer = () => {
    console.log('🎬 GlobalVideoPlayer: 关闭全局媒体播放器')
    setGlobalVideoPlayer({
      open: false,
      mediaType: undefined,
      video: undefined,
      imageUrl: undefined,
      poster: undefined,
    })
  }

  /**
   * 切换媒体播放器状态
   */
  const toggleVideoPlayer = () => {
    if (globalVideoPlayer.open) {
      closeVideoPlayer()
    } else if (globalVideoPlayer.video || globalVideoPlayer.imageUrl) {
      setGlobalVideoPlayer(prev => ({ ...prev, open: true }))
    }
  }

  /**
   * 设置视频内容（不自动打开）
   * @param video 要播放的视频任务
   * @param poster 海报图片URL
   */
  const setVideoContent = (
    video: MyMirrorAiTask,
    poster?: string | null | undefined
  ) => {
    console.log('🎬 GlobalVideoPlayer: 设置视频内容', {
      video: video.id,
      poster,
    })
    setGlobalVideoPlayer(prev => ({
      ...prev,
      mediaType: MediaType.VIDEO,
      video,
      poster,
      imageUrl: undefined,
    }))
  }

  /**
   * 设置图片内容（不自动打开）
   * @param imageUrl 要显示的图片URL
   */
  const setImageContent = (imageUrl: string) => {
    console.log('🖼️ GlobalVideoPlayer: 设置图片内容', { imageUrl })
    setGlobalVideoPlayer(prev => ({
      ...prev,
      mediaType: MediaType.IMAGE,
      imageUrl,
      video: undefined,
      poster: undefined,
    }))
  }

  return {
    // 状态
    isOpen: globalVideoPlayer.open,
    mediaType: globalVideoPlayer.mediaType,
    currentVideo: globalVideoPlayer.video,
    currentImageUrl: globalVideoPlayer.imageUrl,
    currentPoster: globalVideoPlayer.poster,

    // 方法
    openVideoPlayer,
    openImageViewer,
    closeVideoPlayer,
    toggleVideoPlayer,
    setVideoContent,
    setImageContent,
  }
}
