import { useEffect, useRef } from 'react'

/**
 * 自定义hook，用于处理页面可见性变化时的轮询逻辑
 * 在iOS WebView环境中，当应用切换到后台再回到前台时，确保轮询能够正确恢复
 */
export const useVisibilityAwarePoll = (
  pollFunction: () => void,
  stopFunction: () => void,
  dependencies: any[] = []
) => {
  const isPollingRef = useRef(false)
  const shouldPollRef = useRef(true)

  const startPoll = () => {
    // 如果已经在轮询或者不应该轮询，则不启动新的轮询
    if (isPollingRef.current || !shouldPollRef.current) {
      return
    }

    isPollingRef.current = true
    pollFunction()
  }

  const stopPoll = () => {
    shouldPollRef.current = false
    stopFunction()
    isPollingRef.current = false
  }

  const resetPollStatus = () => {
    isPollingRef.current = false
  }

  useEffect(() => {
    // 初始启动轮询
    startPoll()

    // 页面可见性变化处理函数
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 页面隐藏时，停止轮询
        console.log('Page hidden, stopping poll')
        shouldPollRef.current = false
        stopFunction()
        isPollingRef.current = false
      } else {
        // 页面可见时，如果之前在轮询，则重新开始轮询
        console.log('Page visible, resuming poll')
        shouldPollRef.current = true
        // 延迟一点时间再开始轮询，确保页面完全恢复
        setTimeout(() => {
          if (shouldPollRef.current && !isPollingRef.current) {
            startPoll()
          }
        }, 100)
      }
    }

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      shouldPollRef.current = false
      stopFunction()
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, dependencies)

  return {
    startPoll,
    stopPoll,
    resetPollStatus,
    isPolling: isPollingRef.current,
    shouldPoll: shouldPollRef.current,
  }
}
