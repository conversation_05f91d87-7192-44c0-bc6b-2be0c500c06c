import { GetMerchantInfoDocument, GetMerchantInfoQuery } from '@/graphqls/types'
import { useImperativeQuery } from './useImperativeQuery'
import { useAtom } from 'jotai'
import { isOfficialMerchantAtom } from '@/stores'

export const useMerchant = () => {
  const [, setIsOfficialMerchant] = useAtom(isOfficialMerchantAtom)
  const getMerchantInfoQuery = useImperativeQuery<GetMerchantInfoQuery>(
    GetMerchantInfoDocument
  )

  const getMerchantInfo = async () => {
    const { data } = await getMerchantInfoQuery()

    setIsOfficialMerchant(
      data?.mirrorMerchantQuery?.info4?.officialOwnership || false
    )
  }
  const getDefaultMerchantInfo = async () => {
    const { data } = await getMerchantInfoQuery()

    return data?.mirrorMerchantQuery?.info4
  }

  return {
    /** 获取商户信息 */
    getMerchantInfo,
    getDefaultMerchantInfo, // 获取商户信息，maze
  }
}
