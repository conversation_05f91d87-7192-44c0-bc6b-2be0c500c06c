import { MyModal } from '@/components/ui/MyModal'
import { graphQLErrorCode } from '@/utils'
import { useState } from 'react'
import { MirrorLoading } from 'wujieai-react-icon'
import { usePreloadResource } from './usePreloadResource'

/** 资源变更后的弹窗提示
 */
export const useValidResourceChange = () => {
  const [resourceChangeOpen, setResourceChangeOpen] = useState(false)
  const { fetchResources } = usePreloadResource()
  const isResourceChange = (err: any) => {
    // 资源变更情况：价格变动 或 模板被删除
    return graphQLErrorCode(err) === 20210001
  }

  const isPrintOrderChange = (err: any) => {
    // 资源变更情况：分体打印机打印了免费的张数
    return graphQLErrorCode(err) === 20210002
  }

  const refetchResource = async () => {
    setResourceChangeOpen(true)
    await fetchResources()
    setTimeout(() => {
      setResourceChangeOpen(false)
    }, 2000)
  }

  const ResourceChangeModal = () => {
    return (
      <MyModal
        open={resourceChangeOpen}
        content={
          <div className=" flex items-center text-[24px] text-center text-neutral-400 font-bold leading-[42px]">
            资源更新中，请稍候再试
            <MirrorLoading className="ml-2  text-neutral-400 h-8 w-8 animate-spin" />
          </div>
        }
        footer={null}
      />
    )
  }
  return {
    /** 判断资源是否变更 */
    isResourceChange,
    /** 资源变更后的Modal */
    ResourceChangeModal,
    resourceChangeOpen,
    setResourceChangeOpen,
    /** 资源提示以及重新获取资源 */
    refetchResource,
    isPrintOrderChange,
  }
}
