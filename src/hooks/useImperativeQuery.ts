import { useCallback } from 'react'
import {
  useApolloClient,
  OperationVariables,
  DocumentNode,
} from '@apollo/client'

export function useImperativeQuery<
  TData = any,
  TVariables = OperationVariables,
>(query: DocumentNode) {
  const client = useApolloClient()

  const imperativelyCallQuery = useCallback(
    (queryVariables?: TVariables, context?: any) => {
      return client.query<TData>({
        query,
        variables: queryVariables as OperationVariables,
        fetchPolicy: 'no-cache',
        context,
      })
    },
    [client, query]
  )

  return imperativelyCallQuery
}
