import {
  taskType<PERSON>tom,
  defaultPrintIndexAtom,
  drawPayOrderAtom,
  isTaskTypeSelectedAtom,
  resultImagesAtom,
  resultOrder<PERSON>tom,
  userUploadImageAtom,
} from '@/stores'
import { useResetAtom } from 'jotai/utils'

/** 清除用户操作留下的atom信息 */
export const useResetUserAtom = () => {
  const resetPrintIndex = useResetAtom(defaultPrintIndexAtom)
  const resetResultImages = useResetAtom(resultImagesAtom)
  const resetResultOrder = useResetAtom(resultOrderAtom)
  const resetUserUploadImage = useResetAtom(userUploadImageAtom)
  const resetDrawPayOrder = useResetAtom(drawPayOrderAtom)
  const resetIsTaskTypeSelected = useResetAtom(isTaskTypeSelectedAtom)
  const resetTaskType = useResetAtom(taskTypeAtom)

  const resetAtom = () => {
    resetPrintIndex()
    resetResultImages()
    resetResultOrder()
    resetUserUploadImage()
    resetDrawPayOrder()
    resetIsTaskTypeSelected()
    resetTaskType()
  }

  return {
    resetAtom,
  }
}
