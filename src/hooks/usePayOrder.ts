import {
  DrawItemFragment,
  DrawOrderPayInfoFragment,
  PrintOrderInput,
  useCreateDrewOrderMutation,
  useCreatePrintOrderMutation,
  PrintOrderPayInfoFragment,
  AiTaskType,
  MirrorOrderTypeEnum,
} from '@/graphqls/types'
import { drawPayOrderAtom, printPayOrderAtom, taskTypeAtom } from '@/stores'
import { useAtom, useSetAtom } from 'jotai'

/** 支付订单 */
export const usePayOrder = () => {
  const [createDrewOrderAction] = useCreateDrewOrderMutation()
  const [createPrintOrderAction] = useCreatePrintOrderMutation()

  const setDrawPayOrder = useSetAtom(drawPayOrderAtom)
  const setPrintPayOrder = useSetAtom(printPayOrderAtom)
  const [taskType] = useAtom(taskTypeAtom)

  /** 创建作画支付订单 */
  const createDrawPayOrder = async ({
    activeTemplate,
    printPrice,
  }: {
    activeTemplate: DrawItemFragment | null | undefined
    /** 打印价格 */
    printPrice: number
  }) => {
    return new Promise(
      async (resolve: (data: DrawOrderPayInfoFragment) => void, reject) => {
        try {
          const res = await createDrewOrderAction({
            variables: {
              param: {
                // 作画价格
                drawPrice: activeTemplate?.price,
                // 打印价格
                printPrice,
                // 总价
                price: activeTemplate?.price + printPrice,
                itemId: activeTemplate?.id,
                orderType:
                  taskType === AiTaskType.VIDEO
                    ? MirrorOrderTypeEnum.VIDEO
                    : MirrorOrderTypeEnum.DRAW,
              },
            },
          })
          const drawOrderCreate =
            res?.data?.mirrorOrderMutation?.drawOrderCreate
          if (drawOrderCreate) {
            setDrawPayOrder(drawOrderCreate)
            resolve(drawOrderCreate)
          } else {
            reject(null)
          }
        } catch (error) {
          reject(error)
        }
      }
    )
  }

  /** 创建打印支付订单 */
  const createPrintPayOrder = async ({ param }: { param: PrintOrderInput }) => {
    return new Promise(
      async (resolve: (data: PrintOrderPayInfoFragment) => void, reject) => {
        try {
          const res = await createPrintOrderAction({
            variables: {
              param,
            },
          })
          if (res?.data?.mirrorOrderMutation?.printOrderCreate) {
            setPrintPayOrder(res?.data?.mirrorOrderMutation?.printOrderCreate)
            resolve(res?.data?.mirrorOrderMutation?.printOrderCreate)
          }
        } catch (error) {
          reject(error)
        }
      }
    )
  }

  return {
    /** 创建作画支付订单 */
    createDrawPayOrder,
    /** 创建打印支付订单 */
    createPrintPayOrder,
  }
}
