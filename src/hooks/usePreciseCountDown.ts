import { useEffect, useState } from 'react'
import { preciseCountDown } from '@/utils'
/** 精确倒计时： */
export const usePreciseCountDown = (
  _count: number,
  /** 倒计时结束时的回调 */
  onFinish?: () => void,
  onCountCb?: (time: number) => void
) => {
  const [count, setCount] = useState(_count)
  const counter = preciseCountDown()

  const startSecond = (timeDiff: number = _count) => {
    counter.count(
      timeDiff,
      time => {
        setCount(time)
        onCountCb?.(time)
      },
      time => {
        setCount(time)
        onFinish?.()
      }
    )
  }

  const clearSecond = () => {
    counter.clear()
  }

  const resetSecond = (timeDiff: number) => {
    setCount(timeDiff)
    startSecond(timeDiff)
  }

  useEffect(() => {
    return () => {
      clearSecond()
    }
  }, [])

  return {
    second: count,
    /** 开始 */
    startSecond,
    /** 清除定时器 */
    clearSecond,
    /** 重置定时器 */
    resetSecond,
  }
}
