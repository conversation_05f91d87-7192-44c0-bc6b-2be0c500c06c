# 魔镜AI任务创建
mutation createMirrorAiTask($param: MirrorAiTaskCreateInput!) {
  mirrorAiTaskMutation {
    mirrorAiTaskCreate(param: $param) {
      baseId
    }
  }
}

mutation retryMirrorAiTask($baseId: Long!) {
  mirrorAiTaskMutation {
    mirrorAiTaskRetry(baseId: $baseId) {
      baseId
    }
  }
}

# 获取魔镜AI任务状态
query getMirrorAiTaskStatus($baseId: Long!) {
  mirrorAiTaskQuery {
    mirrorAiTaskDetail(baseId: $baseId) {
      id
      status
      succeedNum
      totalNum
      support3DModeling
      templateInfo {
        fitNumber
        direction
        categoryInfo {
          name
        }
      }
      order {
        ...mirrorAiTaskDetailOrder
      }
      details {
        ...mirrorAiTaskDetail
      }
    }
  }
}

# 获取小程序提交的打印队列
query getWeappPrintTaskList($size: PrintTemplateSize) {
  mirrorAiTaskQuery {
    deviceNextPrintTaskList(size: $size) {
      ...mirrorPrintTask
    }
  }
}

mutation startPrintTask($printTaskId: Long) {
  mirrorAiTaskMutation {
    status: startPrintTask(printTaskId: $printTaskId)
  }
}
