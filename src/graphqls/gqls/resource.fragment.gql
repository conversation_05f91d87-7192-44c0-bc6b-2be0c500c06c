# 模型主题
fragment resourcePackage on ResourcePackage {
  id
  hash
  photograph {
    shotNum
  }
  imageNum
  printPriceInfo {
    a4Price
    sixPrice
  }
  categoryList {
    ...categoryList
  }
  categoryVideoList {
    ...categoryList
  }
  rotationCycle
  silencePage
  verticalSilencePage
  bgColor
  bgUrl
  verticalBgUrl
  buttonTextColor
  logoUrl
  textColor
  themeColor
  classificationMode
  videoOpen
  receiveQRCode
  direction
}

fragment drawItem on DrawItem {
  exampleImage
  id
  image
  coverUrl
  faceUrl
  showUrl
  name
  price
  sex
  sort
  fitNumber
  direction
  populationV2
  faceUrl
  videoDuration
}

fragment categoryList on MirrorCategory {
  id
  name
  itemList {
    ...drawItem
  }
}

## 视频资源
fragment videoResourcePackage on ResourcePackage {
  categoryVideoList {
    ...categoryList
  }
}
