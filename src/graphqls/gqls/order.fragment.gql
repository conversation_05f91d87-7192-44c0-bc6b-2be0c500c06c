fragment printOrderDetail on PrintOrder {
  drewOrderId
  id
  printInfoList {
    childOrderId
    mirrorAiTask {
      id
      resultUrl
      status
    }
  }
  status
  totalFee
}

fragment drawOrderDetail on DrawOrder {
  id
  item {
    ...drawItem
  }
  status
  totalFee
}

fragment drawOrderPayInfo on DrawOrderPayInfo {
  order {
    bindAuth
    id
    item {
      ...drawItem
    }
    status
    totalFee
  }
  payInfo {
    expireSecond
    payUrl
    needPay
  }
}

fragment printItem on MirrorPrintInfo {
  childOrderId
  mirrorAiTask {
    id
    resultUrl
    previewUrl
    status
    editResultUrls
  }
}

fragment printOrderPayInfo on PrintOrderPayInfo {
  order {
    drewOrderId
    id
    printInfoList {
      ...printItem
    }
    status
    totalFee
  }
  payInfo {
    expireSecond
    payUrl
    needPay
  }
}
