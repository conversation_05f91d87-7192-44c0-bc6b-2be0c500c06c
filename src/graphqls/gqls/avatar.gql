query getAvatarConfig {
  mirrorAiTaskQuery {
    mirrorAiTaskEditResource {
      backgroundColor {
        id
        label
      }
      size {
        id
        label
      }
    }
  }
}

mutation genAvatar($detailId: Long!, $param: MirrorAiTaskEditInput!) {
  mirrorAiTaskMutation {
    mirrorAiTaskEdit(detailId: $detailId, param: $param)
  }
}

mutation saveAvatar($editId: Long!) {
  mirrorAiTaskMutation {
    mirrorAiTaskEditSave(editId: $editId)
  }
}

query getAvatarDetail($editId: Long!) {
  mirrorAiTaskQuery {
    mirrorAiTaskEdit(editId: $editId) {
      completeTime
      id
      queueTime
      resultUrls
      status
    }
  }
}
