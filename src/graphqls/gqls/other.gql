# 图片检测
mutation checkImage($param: MirrorImageCheckInput) {
  mirrorAiTaskMutation {
    mirrorImageCheck(input: $param) {
      imageDetectionCheck
      imageDetectionId
      imageFaceCheck
      imageDetectionMessage
      imageFaceCheckMessage
    }
  }
}

# h5提交的照片轮询
query getUploadPic($orderId: Long!, $uploadKey: String!) {
  mirrorAiTaskQuery {
    mirrorAiTaskPicUploadResult(
      param: { orderId: $orderId, uploadKey: $uploadKey }
    ) {
      resultUrl
    }
  }
}

# 七牛上传token
query qiniuToken(
  $originalFileName: String
  $key: String
  $bucket: OssBucket
  $dirName: String
) {
  upToken: ossUploadToken(
    param: {
      originalFileName: $originalFileName
      fileName: $key
      bucket: $bucket
      dirName: $dirName
    }
  ) {
    region
    token
  }
}

# 上报打印成功
mutation reportPrintSuccess(
  $ids: [Long!]
  $printOrderId: Long
  $failMessage: String
) {
  mirrorOrderMutation {
    status: printChildOrderStatus(
      childOrderIdList: $ids
      printOrderId: $printOrderId
      failMessage: $failMessage
    )
  }
}

# 获取资源包hash
query getResourceHash {
  deviceQuery {
    templateHash
  }
}

# 上报机器信息
mutation deviceReport($param: DeviceHardwareStatusReportInput!) {
  deviceMutation {
    status: deviceReport(param: $param)
  }
}

# 设备信息
query deviceInfo {
  deviceQuery {
    deviceInfo {
      printerEnable
      cameraEnable
      deviceType
      # 机器唯一Id
      id
      # 设备临时id。
      #注意：只有id + sessionId 跟后台设备管理一致，说明device-token是最新的，否则就是旧的
      sessionId
      splitPrinting
      direction
      phone
      multiScreenDisplay
      # 是否配置竖屏摄像头
      cameraConfig
    }
  }
}

# 获取公众号二维码
query getOfficialAccountQRCode($bindAuth: String) {
  deviceQuery {
    getOffiaccountQRCode(bindAuth: $bindAuth) {
      url
      expireSeconds
    }
  }
}

# 获取商户信息
query getMerchantInfo {
  mirrorMerchantQuery {
    info4 {
      name
      no
      officialOwnership
    }
  }
}

# 获取微信小程序二维码
query mirrorWeappCodeUrl($input: QrCodeInput) {
  weappQuery {
    mirrorWeappCodeUrl(input: $input)
  }
}
