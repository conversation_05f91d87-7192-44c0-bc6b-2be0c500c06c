mutation gen3d($param: ThreeDModelingInput!) {
  mirrorAiTaskMutation {
    mirrorAiTask3DModeling(param: $param)
  }
}

mutation save3d($id: Long!) {
  mirrorAiTaskMutation {
    mirrorAiTask3DModelingSave(id: $id)
  }
}

fragment threeDdetail on ThreeDModelingInfo {
  id
  startTime
  expectedTime
  renderedImage
  modelUrl
  status
  generateType
}

query get3dDetail($id: Long!) {
  mirrorAiTaskQuery {
    mirrorAiTask3DModelingInfo(id: $id) {
      ...threeDdetail
    }
  }
}
