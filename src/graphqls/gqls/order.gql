# 获取作画订单详情
query getMirrorDrawOrder($orderId: Long!) {
  mirrorOrderQuery {
    deviceDrawOrder(orderId: $orderId) {
      ...drawOrderDetail
    }
  }
}

# 获取打印订单详情
query getMirrorPrintOrder($orderId: Long!) {
  mirrorOrderQuery {
    devicePrintOrder(orderId: $orderId) {
      ...printOrderDetail
    }
  }
}

# 创建作画订单
mutation createDrewOrder($param: DrawOrderInput!) {
  mirrorOrderMutation {
    drawOrderCreate(param: $param) {
      ...drawOrderPayInfo
    }
  }
}

# 取消订单
mutation cancelOrder($orderId: Long) {
  mirrorOrderMutation {
    success: orderCancel(orderId: $orderId)
  }
}

# 创建打印订单
mutation createPrintOrder($param: PrintOrderInput!) {
  mirrorOrderMutation {
    printOrderCreate(param: $param) {
      ...printOrderPayInfo
    }
  }
}
