# 单个任务详情
fragment mirrorAiTaskDetail on MirrorAiTaskDetail {
  id
  detailType
  resultUrl
  previewUrl
  status
  expectedQueueSeconds
  generatingCompletePercent
  editResultUrls
  threeDModelingInfo {
    modelUrl
  }
}
# 单个任务详情-订单
fragment mirrorAiTaskDetailOrder on MirrorBaseOrder {
  printOrderNum
  printDiscountFee
  printPriceSnapshot {
    sixPrice
    a4Price
  }
}

# 单个打印task
fragment mirrorPrintTask on MirrorPrintTask {
  id
  printImage {
    printUrl
    recordId
    size
  }
  orderInfo {
    id
  }
  taskInfo {
    templateInfo {
      direction
    }
  }
}
