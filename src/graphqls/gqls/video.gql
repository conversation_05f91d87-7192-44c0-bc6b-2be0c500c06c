# 获取机器下一个视频播放任务
query getVideoPlayTaskList {
  mirrorAiTaskQuery {
    deviceNextVideoPlay {
      ...mirrorVideoTaskItem
    }
  }
}

# 上报-视频开始播放
mutation startVideoPlayTask($taskId: Long) {
  mirrorAiTaskMutation {
    status: startVideoPlayTask(taskId: $taskId)
  }
}

# 上报-视频结束播放
mutation finishVideoPlayTask($status: MirrorAiTaskStatus, $taskId: Long) {
  mirrorAiTaskMutation {
    status: finishVideoPlayTask(status: $status, taskId: $taskId)
  }
}

# 开启投送
mutation createVideoPlayTask($baseId: Long) {
  mirrorAiTaskMutation {
    id: createVideoPlayTask(baseId: $baseId)
  }
}

# 预约投送
mutation reserveVideoPlayTask($baseId: Long) {
  mirrorAiTaskMutation {
    status: reserveVideoPlayTask(baseId: $baseId)
  }
}
