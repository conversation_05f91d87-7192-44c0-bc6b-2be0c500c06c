import {
  ApolloClient,
  InMemoryCache,
  ApolloLink,
  HttpLink,
  ServerError,
  from,
  Observable,
} from '@apollo/client'
import ApolloLinkTimeout from 'apollo-link-timeout'
import { onError } from '@apollo/link-error'
import { getMockUri } from '@/utils/mockUtil'
import Cookies from 'js-cookie'
import { DEVICE_TOKEN, LANGUAGE } from '@/configs'
import { getAuth, randomRange } from '@/utils'

const uri = import.meta.env.VITE_GRAPHQL_API

const httpLink = new HttpLink({
  uri,
  credentials: 'same-origin',
})
// 接口超时默认时间
const timeoutHttpLink = new ApolloLinkTimeout(60000).concat(httpLink)

const authLink = new ApolloLink((operation, forward) => {
  const deviceToken = Cookies.get(DEVICE_TOKEN)
  if (!deviceToken) {
    // 不发请求，直接返回一个空的 Observable，表示中止
    return new Observable(observer => {
      observer.error(new Error('Missing device token, request aborted.'))
    })
  }
  operation.setContext(({ headers }: { headers: any }) => ({
    uri:
      getMockUri(uri, operation.operationName) + '/' + operation.operationName,
    headers: {
      ...headers,
      from: 'web',
      'app-code': 'trade',
      device_id: randomRange(20, 20),
      authorization: getAuth(),
      'device-token': Cookies.get(DEVICE_TOKEN),
      language: LANGUAGE,
    },
  }))

  return forward(operation)
})

const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors) {
    graphQLErrors.forEach(({ message }) => {
      console.log(`[GraphQL error]: Message: ${message}`)
    })
  }

  if (networkError) {
    const code = (networkError as ServerError as any)?.result?.code

    console.log(`[Network error]: ${networkError}`)
    console.log(`[Network error code]: ${code}`)
  }
})

const cleanTypeName = new ApolloLink((operation, forward) => {
  if (operation.variables) {
    const omitTypename = (key: string, value: any) =>
      key === '__typename' ? undefined : value

    operation.variables = JSON.parse(
      JSON.stringify(operation.variables),
      omitTypename
    )
  }
  return forward(operation)
})

const link = from([cleanTypeName, errorLink, authLink, timeoutHttpLink])

export const client = new ApolloClient({
  link,
  cache: new InMemoryCache(),
})
