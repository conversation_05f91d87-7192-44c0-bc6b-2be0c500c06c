@import './tailwind.css';
@import './shadcn-ui.css';

html {
  height: 100%;
  font-size: 16px;
  font-family:
    <PERSON><PERSON><PERSON>,
    PingFangSC-Regular,
    Helvetica Neue,
    Microsoft YaHei,
    '微软雅黑',
    sans-serif,
    Helvetica,
    sans-serif;
}
body {
  height: 100%;
  @apply text-lg text-neutral-50;
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: cover;
  background-color: rgb(23, 23, 23);
}
html * {
  box-sizing: border-box;
  outline: 0;
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

#root {
  width: 100%;
  height: 100%;
  @apply flex justify-center items-center;
}

:focus {
  outline: none;
}

.scrollbar-hidden {
  &::-webkit-scrollbar {
    display: none;
  }
  &::-webkit-scrollbar-thumb {
    display: none;
  }
  &::-webkit-scrollbar-track {
    display: none;
  }
  scrollbar-width: none;
}
