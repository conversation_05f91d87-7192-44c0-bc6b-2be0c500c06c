import { isWeixin } from './ua'

/**生成随机字符串 */
export function randomRange(min: number, max: number) {
  let returnStr = ''
  const range = max ? Math.round(Math.random() * (max - min)) + min : min,
    arr = [
      '0',
      '1',
      '2',
      '3',
      '4',
      '5',
      '6',
      '7',
      '8',
      '9',
      'a',
      'b',
      'c',
      'd',
      'e',
      'f',
      'g',
      'h',
      'i',
      'j',
      'k',
      'l',
      'm',
      'n',
      'o',
      'p',
      'q',
      'r',
      's',
      't',
      'u',
      'v',
      'w',
      'x',
      'y',
      'z',
      'A',
      'B',
      'C',
      'D',
      'E',
      'F',
      'G',
      'H',
      'I',
      'J',
      'K',
      'L',
      'M',
      'N',
      'O',
      'P',
      'Q',
      'R',
      'S',
      'T',
      'U',
      'V',
      'W',
      'X',
      'Y',
      'Z',
    ]
  for (let i = 0; i < range; i++) {
    const index = Math.round(Math.random() * (arr.length - 1))
    returnStr += arr[index]
  }
  return returnStr
}

/**获取cookie对象 */
export function getCookies(cookie: string) {
  const cookies: Record<string, string> = {}
  ;(cookie || '').split(';').forEach((item: string) => {
    item = item.replace(/'/g, '').replace(/ /g, '')
    const key = item.split('=')[0]
    const value = item.split('=')[1]
    cookies[key] = value
  })
  return cookies
}

/**graphql错误信息处理 */
export const graphQLErrorMessage = (error: any) => {
  // 断网或者超时错误提示
  if (
    error?.message === 'Failed to fetch' ||
    error?.message === 'Timeout exceeded'
  ) {
    return '网络错误，请检查网络连接后重试...'
  }
  /**跟后端约定：code末尾为0000代表可使用接口给的message，否则用前端自定义的兜底message */
  if (Number(error?.graphQLErrors?.[0]?.extensions?.code) % 10000 === 0) {
    return (
      error?.graphQLErrors?.[0]?.extensions?.message ||
      error?.graphQLErrors?.[0].message
    )
  }
  return ''
}

export const graphQLErrorCode = (error: any) => {
  return Number(error?.graphQLErrors[0]?.extensions?.code || -1)
}

export const isNotFound = (error: any) => {
  return Number(error?.graphQLErrors?.[0]?.extensions?.code) === 200200081
}

/**获取错误码 */
export const getErrorCode = (error: any) => {
  if (isNotFound(error)) {
    return 404
  }

  return 500
}

/**
 * 预加载图片资源
 * @param imgs 图片url列表
 * @returns
 */
export function preLoadImages(imgs: string[]) {
  imgs = imgs?.filter(it => !!it)
  if (!imgs.length) return Promise.resolve(false)
  let count = 1
  return new Promise(resolve => {
    for (let i = 0; i < imgs.length; i++) {
      const img = new Image()
      img.src = imgs[i]
      img.onload = function () {
        if (count++ === imgs.length) {
          resolve(true)
        }
      }
      img.onerror = function () {
        resolve(false)
      }
    }
  })
}
/**
 * 预加载视频资源
 * @param imgs 图片url列表
 * @returns
 */
export function preLoadVideos(videos: string[]) {
  videos = videos?.filter(it => !!it)
  if (!videos.length) {
    return Promise.resolve(true)
  }
  let count = 0
  return new Promise(resolve => {
    for (let i = 0; i < videos.length; i++) {
      const video = document.createElement('video')
      video.src = videos[i]
      video.preload = 'auto'

      /** 备注：微信浏览器不支持onloadedmetadata */
      if (isWeixin()) {
        count++
        if (count === videos.length) {
          resolve(true)
        }
      } else {
        video.onloadedmetadata = function () {
          count++
          if (count === videos.length) {
            resolve(true)
          }
        }
      }

      video.onerror = function () {
        resolve(false)
      }
    }
  })
}

/** 判断 a 是否是 null 或者 undefined, 排除 a 为 0 的情况 */
export function isNullOrUndefined(a: any): boolean {
  if (a === null) {
    return true
  }
  if (a === undefined) {
    return true
  }

  return false
}

export function uniqueBy(arr: any[], key: (val: any) => any) {
  const seen = new Set()
  return arr.filter(item => {
    const k = key(item)
    return seen.has(k) ? false : seen.add(k)
  })
}
/**
 * 睡眠
 */
export function sleep(time: number): Promise<void> {
  return new Promise(resolve => {
    const timer = setTimeout(() => {
      clearTimeout(timer)
      resolve()
    }, time)
  })
}

/** 版本比较 */
export const compareVersion = (v1: string, v2: string) => {
  if (v1 === v2) {
    return 0
  }

  const vs1 = v1.split('.').map(a => parseInt(a))
  const vs2 = v2.split('.').map(a => parseInt(a))

  const length = Math.min(vs1.length, vs2.length)
  for (let i = 0; i < length; i++) {
    if (vs1[i] > vs2[i]) {
      return 1
    } else if (vs1[i] < vs2[i]) {
      return -1
    }
  }

  if (length === vs1.length) {
    return -1
  } else {
    return 1
  }
}

export const copy = async (text: string): Promise<void> => {
  try {
    await navigator.clipboard.writeText(text)
  } catch (err) {
    console.error('复制失败:', err)
  }
}
