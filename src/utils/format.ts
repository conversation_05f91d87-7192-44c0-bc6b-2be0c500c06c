import dayjs from 'dayjs'
import { APP_NO_CACHE_TAG, IMAGE_CDN } from '@/configs'
import accurateCalc from 'accurate-core'
import { isMirror } from './ua'

console.log('env', import.meta.env)
// 线上打包环境接口
export const isProdApi =
  import.meta.env.PROD && import.meta.env.MODE === 'production'

/** 获取文件后缀 */
export const getFileExtension = (url: string) => {
  return url.split('.').pop() ?? ''
}

export const fenToYuan = (num: number | string | undefined | null) => {
  return Number.parseFloat(accurateCalc.divide(Number(num), 100).toFixed(2))
}

/** mp4文件 */
export const isMP4 = (url: string) => {
  return getFileExtension(url) === 'mp4'
}

/** svg文件 */
export const isSvg = (url: string) => {
  return getFileExtension(url) === 'svg'
}

/** 是否能转换成webp格式 */
export const canConvertWebp = (url: string) => {
  return ['png', 'jpeg', 'jpg', 'gif'].includes(getFileExtension(url))
}

/** 缩略图： 支持情况下转，否则原图 */
const toViewImage = (url: string, width: number) => {
  return canConvertWebp(url) ? `${url}?imageView2/2/w/${width}/q/75` : url
}

export const to400Image = (url: string) => {
  return toViewImage(url, 400)
}

export const to800Image = (url: string) => {
  return toViewImage(url, 800)
}

export const to1200Image = (url: string) => {
  return toViewImage(url, 1200)
}

export const to1440Image = (url: string) => {
  return toViewImage(url, 1440)
}

// 带水印的图
export const toMarkImage = (url: string) => {
  return `${url}?imageView2/2/w/1200/q/75|watermark/1/image/aHR0cHM6Ly9kYXRhLnd1amllYmFudHUuY29tL2xvZ28lRTYlQjAlQjQlRTUlOEQlQjAlMjAyLnBuZw==/dissolve/100/gravity/SouthWest/wst/1/ws/0.083`
}

/** 图片用webcdn */
export const toCDNImage = (url: string) => {
  return isProdApi ? IMAGE_CDN + url : url
}

// 旋转图片
export const toRotateImage = (url: string) => {
  if (url.includes('imageMogr2/rotate')) return url
  return `${url}?imageMogr2/rotate/270`
}

export const toYMDHms = (timestrap: number) => {
  return dayjs(timestrap * 1000).format('YYYY-MM-DD HH:mm:ss')
}

export const toYMDHm = (timestrap: number) => {
  return dayjs(timestrap * 1000).format('YYYY-MM-DD HH:mm')
}

export const toYMD = (timestrap: number) => {
  return dayjs(timestrap * 1000).format('YYYY-MM-DD')
}

export const toHm = (timestrap: number) => {
  return dayjs(timestrap * 1000).format('HH:mm')
}

export const formatTime = (timestrap: number, format: string) => {
  return dayjs(timestrap * 1000).format(format)
}

export const queueTimeShow = (second: number) => {
  if (second < 60) {
    return `${second}秒`
  } else if (second < 3600) {
    return `${Math.floor(second / 60)}分钟`
  } else {
    return `${Math.floor(second / 3600)}小时`
  }
}

export function hexToRgb(hex: string) {
  // Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
  const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i

  const hex2 = hex.replace(shorthandRegex, function (_m, r, g, b) {
    return r + r + g + g + b + b
  })

  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex2)

  return result
    ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16),
      }
    : null
}

/** 视频封面 */
export const toVideoPoster = (url: string) => {
  if (!url) return url
  return `${url}?vframe/jpg/offset/0.001`
}

/** 添加app 不进行缓存标识
 * 说明： 默认app对图片，视频会进行缓存
 * 只有增加了不缓存的标识，才会pass
 */
export function toAppNoCacheTag(url: string) {
  if (!isMirror() || !url) return url

  if (url.includes('#')) {
    // URL 带有 #
    const [base, fragment] = url.split('#', 2)
    console.log('base', base, 'fragment', fragment)
    if (base.includes('?')) {
      // 带有 ?
      return `${base}&${APP_NO_CACHE_TAG}#${fragment}`
    } else {
      // 不带 ?
      return `${base}?${APP_NO_CACHE_TAG}#${fragment}`
    }
  } else if (url.includes('?')) {
    // URL 带有 ?
    return `${url}&${APP_NO_CACHE_TAG}`
  } else {
    // 不带 ? 和 #
    return `${url}?${APP_NO_CACHE_TAG}`
  }
}
