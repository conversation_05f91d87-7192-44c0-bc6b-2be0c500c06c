import { DEVICE_TYPE, VIRTUAL_UID } from '@/configs'
import Cookies from 'js-cookie'
/** 判断设备类型 */
const deviceType = (reg: any) => {
  // for ssr
  if (typeof navigator === 'undefined') {
    return false
  }
  const ua = navigator.userAgent.toLowerCase()

  return reg.test(ua)
}

export const isWeixin = () => {
  return deviceType(/MicroMessenger/i)
}

export const isIOS = () => {
  if (typeof navigator === 'undefined') {
    return false
  }
  const ua = navigator.userAgent.toLowerCase()
  /** https://stackoverflow.com/questions/57765958/how-to-detect-ipad-and-ipad-os-version-in-ios-13-and-up */
  return (
    /(iphone|ipad|ipod)/i.test(ua) /* iOS pre 13 */ ||
    (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1)
  ) /* iPad OS 13 */
}

/** 安卓机器 */
export const isMachine = () => Cookies.get('platform') === 'mirror-shell'
/** 安卓 Pad */
export const isPad = () => Cookies.get('platform') === 'mirror-shell-pad'
/** 苹果 IPad */
function isRealIPad() {
  const ua = navigator.userAgent
  const isMac = /Macintosh/.test(ua)
  const hasTouch = navigator.maxTouchPoints && navigator.maxTouchPoints > 1
  return isMac && hasTouch
}

export const isIPad = () =>
  Cookies.get('platform') === 'mirror-shell-ipad' || isRealIPad() || isPad()

/** 判断是安卓-拍照机器 */
export const isAndroidMirror = () => isMachine() || isPad()

/** 判断是否是拍照机机器 */
export const isMirror = () => isAndroidMirror() || isIPad() || isIphone()

export const isPhone = () => {
  return (
    deviceType(/Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i) &&
    window.innerWidth < 750
  )
}

export const isIphone = () => {
  return Cookies.get('platform') === 'mirror-shell-iphone'
}

export const isWebApp = () => {
  return isPhone() && !isIphone()
}

export const isDevDevice = () => {
  return /:16661/.test(window.location.href)
}
// 判断是否为 webapp 虚拟设备
export const isWebAppDevice = () => {
  return Cookies.get(DEVICE_TYPE) === 'webapp'
}
