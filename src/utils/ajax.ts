import axios from 'axios'
import Cookies from 'js-cookie'
import { getAuth } from './auth'
const headers = {
  Accept: 'application/json;charset=utf-8',
}
const ajax = axios.create({
  withCredentials: false,
  headers,
})

ajax.interceptors.request.use(
  function (config) {
    const authorization = Cookies.get('token') // maze token
    const deviceToken = Cookies.get('device-token') // maze token
    const wjAuthorization = getAuth() // wj token
    const i18nextLng = Cookies.get('language')
    // const original = JSON.stringify({ appId: '1', timestamp: parseInt(+new Date() / 1000, 10) });
    // const sign = _util.encrypt(original);
    // config.headers['GatewayAuth'] = JSON.stringify({ sign, secretKeyVersion: 'v1.1', original });
    if (authorization) {
      config.headers['authorization'] = `Bearer ${authorization}`
      config.headers['wj-authorization'] = `${wjAuthorization}`
      config.headers['device-token'] = `${deviceToken}`
    }
    if (config.params) {
      config.params['language'] = i18nextLng
    } else {
      config.params = { language: i18nextLng }
    }
    return config
  },
  function (error) {
    return Promise.reject(error)
  }
)

// ajax.interceptors.response.use(
//   response => {
//     if (response.status === 200) {
//       const { data } = response
//       return data
//     } else {
//       return Promise.reject(response)
//     }
//   },
//   function (error) {
//     return Promise.reject(error)
//   }
// )

export default ajax
