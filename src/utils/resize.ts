const resizeWidth = 1920
const resizeHeight = 1080
import { isIOS } from '@/utils'
let iosScale = 1
export function resize() {
  // 视口宽高
  const windowW = document?.documentElement?.clientWidth
  const windowH = document?.documentElement?.clientHeight
  // console.log('document', document?.documentElement)

  let scale = 1
  const radio = windowW / windowH
  if (radio > 1) {
    // 横屏
    if (radio > resizeWidth / resizeHeight) {
      // 高度撑满
      scale = windowH / resizeHeight
    } else {
      // 宽度撑满
      scale = windowW / resizeWidth
    }
  } else {
    // 竖屏
    if (radio < resizeHeight / resizeWidth) {
      // 宽度撑满
      scale = windowW / resizeHeight
    } else {
      // 高度撑满
      scale = windowH / resizeWidth
    }
  }

  if (isIOS()) {
    // ios设备采用scale方案
    iosScale = scale
  } else {
    // 非ios设备，采用zoom缩放
    // document?.body?.style?.setProperty('zoom', scale + '')
  }

  return {
    scale: 1,
    /** 是否横屏 */
    // isLandscape: windowW > windowH ? true : false,
  }
}

/** 全局scale样式获取 */
export const getScaleStyle = (style: React.CSSProperties) => {
  if (iosScale === 1) return {}
  return style
}
