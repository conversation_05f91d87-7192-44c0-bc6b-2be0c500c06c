import Compressor from 'compressorjs'

export interface CompressorOptionsProps {
  quality?: number
  maxWidth?: number
  convertSize?: number
}

export const COMPRESSOR_OPTIONS = {
  quality: 0.8,
  maxWidth: 2048,
  convertSize: 1000000, // 1MB
}

/** 压缩图片 */
export const compressorImage = (
  file: File,
  options?: CompressorOptionsProps
) => {
  return new Promise((resolve, reject) => {
    new Compressor(file, {
      quality: options?.quality || COMPRESSOR_OPTIONS.quality,
      maxWidth: options?.maxWidth || COMPRESSOR_OPTIONS.maxWidth,
      convertSize: options?.convertSize || COMPRESSOR_OPTIONS.convertSize, // 超过图片大小限制会转成jpg
      success(result) {
        const newFile = new File([result], (result as File).name, {
          type: result.type,
        })
        resolve(newFile)
      },
      error(err) {
        reject(err)
        console.log(err && err.message)
      },
    })
  })
}

export const downloadRemoteFile = (url: string, name: string = 'maze') => {
  fetch(url)
    .then(res => res.blob())
    .then(blob => {
      const a = document.createElement('a')
      const objectUrl = window.URL.createObjectURL(blob)
      a.download = name
      a.href = objectUrl
      a.click()
      window.URL.revokeObjectURL(objectUrl)
      a.remove()
    })
}
