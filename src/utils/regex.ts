export const isMobile = /^1\d{10}$/

export const isTradePwd = /^\d{6}$/

export const isURL = /^https?:\/\//

export const isAmount = /^(\d+)(.\d{0,2})?$/

/** 图片格式后缀正则 */
export const imageTypeRegex = /\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$/i
/** 视频格式后缀正则 */
export const videoRegex = /\.(mp4|mov|wmv|flv|avi|mkv|webm|m4v)(\?.*)?$/i
/** 校验是否为视频文件 */
export const validateIsVideo = (url?: string) => {
  return videoRegex.test(url || '')
}
