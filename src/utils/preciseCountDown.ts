/**
 * 补零
 * @param time 时间
 */
export const formatTimeWithZero = (time: number): string => {
  if (time >= 10) {
    return time + ''
  } else {
    return `0${time}`
  }
}
/**
 * 分秒换算
 * @param time 时间
 */
export const formateSecondTime = (time: number): string => {
  const m: string = formatTimeWithZero(Math.floor(time / 60) % 60) // 分
  const s: string = formatTimeWithZero(Math.floor(time % 60)) // 秒
  return `${m}:${s}`
}

/** 精确倒计时 */
export const preciseCountDown = () => {
  let timer: number | null = null
  const interval = 1000

  const clear = () => {
    if (timer) {
      window.clearTimeout(timer)
      timer = null
    }
  }
  /**
   * 画画倒计时
   * @param timeDiff 倒计时总时间 单位s
   * @param countF 计时中函数
   * @param endF 倒计时结束
   */
  const count = (
    timeDiff: number,
    countF?: (timeDiff: number) => void,
    endF?: (timeDiff: number) => void
  ) => {
    const startTime = new Date().getTime()
    let countTime = 0
    let currentInterval = interval
    if (
      (countF && typeof countF !== 'function') ||
      (endF && typeof endF !== 'function')
    ) {
      throw Error('回调函数需要是function')
    }
    function loop() {
      countTime++
      // 时间误差
      const offset = new Date().getTime() - (startTime + countTime * interval)
      currentInterval = interval - offset

      timeDiff--
      if (timeDiff <= 0) {
        endF?.(timeDiff)
        clear()
        return
      } else {
        countF?.(timeDiff)
      }

      timer = window.setTimeout(loop, currentInterval)
    }
    clear()
    timer = window.setTimeout(loop, currentInterval)
  }
  return {
    clear,
    count,
    timer,
  }
}
