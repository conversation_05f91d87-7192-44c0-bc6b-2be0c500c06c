/// <reference types="react-scripts" />
interface ImportMetaEnv {
  readonly DEV: boolean
  readonly PROD: boolean
  readonly MODE: 'production' | 'development' | 'test'
  readonly VITE_GRAPHQL_API: string
  readonly VITE_CLIENT_HOST: string
  readonly VITE_MOBILE_HOST: string
  readonly VITE_MAZE_HOST: string
  // more env variables...
}
interface ImportMeta {
  readonly env: ImportMetaEnv
}

interface Window {
  appCallbackId: number
  _hmt: any
  magpiesBridge: {
    query: (cmd: string, callback: any) => void
  }
  webkit: any
}
