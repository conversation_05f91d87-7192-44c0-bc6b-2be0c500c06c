module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'prettier' // 直接在 extends 里添加
  ],
  ignorePatterns: ['dist', '.eslintrc.cjs'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh', 'prettier'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
    'react-hooks/exhaustive-deps': 0,
    '@typescript-eslint/no-explicit-any': 0,
    'no-async-promise-executor': 0,
    'no-unused-vars': 'off', // 关闭基础规则，使用 TypeScript 版本
    '@typescript-eslint/no-unused-vars': 'warn',
    'no-extra-semi': 0,
    'prettier/prettier': 'error', // 让 prettier 作为 ESLint 规则生效
  },
};
