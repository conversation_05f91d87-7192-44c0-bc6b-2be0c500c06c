<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100" fill="none">
  <!-- 背景模糊效果圆形 - 使用多层半透明圆形模拟模糊效果 -->
  <circle cx="50" cy="50" r="50" fill="url(#paint0_radial_blur)" fill-opacity="0.15"/>
  <circle cx="50" cy="50" r="49" fill="url(#paint0_radial_blur)" fill-opacity="0.1"/>
  <circle cx="50" cy="50" r="48" fill="url(#paint0_radial_blur)" fill-opacity="0.08"/>

  <!-- 主要背景圆形 -->
  <circle cx="50" cy="50" r="48.5" fill="url(#paint0_linear_8336_49109)" fill-opacity="0.4" stroke="#EDF0F4" stroke-width="3"/>

  <!-- 箭头路径 -->
  <path d="M60.4727 26.1914L36.9024 49.7616L60.4727 73.3319" stroke="white" stroke-width="7.14286" stroke-linecap="round" stroke-linejoin="round"/>

  <defs>
    <!-- 模糊效果的径向渐变 -->
    <radialGradient id="paint0_radial_blur" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#FFFFFF" stop-opacity="0.3"/>
      <stop offset="70%" stop-color="#CCCCCC" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="#999999" stop-opacity="0.05"/>
    </radialGradient>

    <!-- 原有的线性渐变 -->
    <linearGradient id="paint0_linear_8336_49109" x1="50" y1="0" x2="50" y2="100" gradientUnits="userSpaceOnUse">
      <stop stop-color="#904DDD"/>
      <stop offset="1" stop-color="#ABC9E3"/>
    </linearGradient>
  </defs>
</svg>