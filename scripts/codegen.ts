import { CodegenConfig } from '@graphql-codegen/cli'
import CryptoJS from 'crypto-js'

const NODE_ENV = process.env.NODE_ENV || 'production'

const endpoints = {
  test: 'https://pref-gate.wujieai.com/one-graph-wujiebantu-auth/graphql',
  development:
    'https://pref-gate.wujieai.com/one-graph-wujiebantu-auth/graphql',
  production: 'https://gate.wujiebantu.com/one-graph-auth/graphql',
}

const ENDPOINT = endpoints[NODE_ENV]
const CRYPTOJSKEY = 'WTAHAPPYACTIVITY'

function encryptForGW(plaintText: string, cryptojsKey: string) {
  const options = {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  }
  const key = CryptoJS.enc.Utf8.parse(cryptojsKey)
  const encryptedData = CryptoJS.AES.encrypt(plaintText, key, options)
  const encryptedBase64Str = encryptedData
    .toString()
    .replace(/\//g, '_')
    .replace(/\+/g, '-')

  return encryptedBase64Str
}

const timestamp = Math.floor(new Date().getTime() / 1000).toString()
const plaintText = JSON.stringify({
  appId: '1',
  timestamp,
  serverCode: '0',
})
const Authorization = JSON.stringify({
  secretKeyVersion: 1,
  sign: encryptForGW(plaintText, CRYPTOJSKEY),
})

const config: CodegenConfig = {
  overwrite: true,
  schema: {
    [ENDPOINT]: {
      headers: {
        authorization: Authorization,
      },
    },
  },
  documents: ['./src/graphqls/**/*.{ts,graphql,gql}'],
  generates: {
    './src/graphqls/types.ts': {
      plugins: [
        'typescript',
        'typescript-operations',
        'typescript-react-apollo',
      ],
      config: {
        onlyOperationTypes: true,
        skipTypename: true,
        scalars: {
          JSONString: 'string',
          UUID: 'string',
          DateTime: 'string',
        },
        dedupeOperationSuffix: true, // Prevent suffix duplication in generated names
        namingConvention: {
          enumValues: 'change-case-all#upperCase', //枚举的值全部大写
        },
      },
    },
  },
  hooks: {
    onError: (err: any) => {
      console.error('Error in GraphQL code generation:', err)
    },
    afterAllFileWrite: () => {
      console.log('\nGql genenrate success!!!')
    },
  },
}

console.log('config', process.env.NODE_ENV, ENDPOINT)

export default config
