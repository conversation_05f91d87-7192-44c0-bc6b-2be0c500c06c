import qiniu from 'qiniu'
import fs from 'fs'
import qiniuConfig from './qiniu.config.js'
import process from 'process'

let config = new qiniu.conf.Config()
config.zone = qiniu.zone[qiniuConfig.zone]
qiniu.conf.RPC_TIMEOUT = 600000

let mac = new qiniu.auth.digest.Mac(
  qiniuConfig.accessKey,
  qiniuConfig.secretKey
)

let argvArr = process.argv.slice(2)
let bucket = qiniuConfig.bucket
let resourceConfig = qiniuConfig[argvArr[0]]
let debugFlag = argvArr[1] === 'debug' ? true : false

let { batch = 10, retryTimes = 3 } = qiniuConfig

let uploadCount = 0
let needUpload = []

//将fs.stat和fs.readdir转换成promise的工具函数
let makePromiseUtil = fn => {
  return (...args) => {
    return new Promise((resolve, reject) => {
      ;[].push.call(args, (err, ret) => {
        if (err) {
          reject(err)
        } else {
          resolve(ret)
        }
      })
      fn.apply(null, args)
    })
  }
}

let statPromise = makePromiseUtil(fs.stat)
let readdirPromise = makePromiseUtil(fs.readdir)

//构建上传策略函数
let uptoken = (bucket, key) => {
  let options = {
    scope: bucket + ':' + key,
  }
  let putPolicy = new qiniu.rs.PutPolicy(options)
  return putPolicy.uploadToken(mac)
}

//构造上传函数
let uploadFile = (uptoken, key, localFile) => {
  let formUploader = new qiniu.form_up.FormUploader(config)
  let putExtra = new qiniu.form_up.PutExtra()

  return new Promise((resolve, reject) => {
    formUploader.putFile(uptoken, key, localFile, putExtra, function (err) {
      if (err) {
        console.log(key + ' upload failed')
        debugFlag && console.error(err)

        if (retryTimes >= 1) {
          resolve('error')
        } else {
          process.exit(1)
        }
      } else {
        uploadCount++

        debugFlag &&
          console.log(uploadCount, needUpload.length, key, 'uploadCount')

        if (uploadCount === needUpload.length) {
          console.log('all file is upload successful')
        }

        resolve()
      }
    })
  })
}

// 批量上传
let uploadFilesByArr = arr => {
  let batchArr = arr.slice(0, batch)

  if (batchArr.length <= 0) {
    return
  }

  Promise.all(
    batchArr.map(path => {
      //要上传文件的本地路径
      let filePath = path
      //上传到七牛后保存的文件名
      let key = path.replace(resourceConfig.originPath, resourceConfig.prefix)
      //生成上传 Tokens
      let token = uptoken(bucket, key)

      //调用uploadFile上传
      return uploadFile(token, key, filePath)
    })
  ).then(res => {
    if (res.includes('error')) {
      retryTimes--
      uploadCount = 0
      uploadFilesByArr(needUpload)
    } else {
      uploadFilesByArr(arr.slice(batch))
    }
  })
}

// 遍历文件
let readFilesFormDir = dir => {
  return statPromise(dir).then(stats => {
    let ret
    if (stats.isDirectory()) {
      ret =
        !/[/]php$/.test(dir) &&
        readdirPromise(dir)
          .then(files => {
            return Promise.all(
              files.map(file => readFilesFormDir(dir + '/' + file))
            )
          })
          .then(paths => {
            return [].concat(...paths)
          })
      ret = ret || []
    } else if (stats.isFile()) {
      ret = dir
    }
    return ret
  })
}

readFilesFormDir(resourceConfig.originPath).then(paths => {
  needUpload.push(...paths)
  console.log('Uploading %s files...', needUpload.length)
  uploadFilesByArr(needUpload)
})
