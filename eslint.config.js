// eslint.config.js
import js from '@eslint/js'
import tseslint from 'typescript-eslint'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import prettier from 'eslint-plugin-prettier'

export default [
  {
    ignores: [
      'dist/**',
      'node_modules/**',
      'scripts/**/*.js',
      'tailwind.config.js',
      'postcss.config.js',
      'src/graphqls/**/*.ts', // 自动生成的GraphQL文件
    ],
  },
  js.configs.recommended, // eslint:recommended
  ...tseslint.configs.recommended, // plugin:@typescript-eslint/recommended
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      prettier: prettier,
    },
    languageOptions: {
      parser: tseslint.parser,
      ecmaVersion: 2020,
      sourceType: 'module',
      globals: {
        window: 'readonly',
        document: 'readonly',
        console: 'readonly',
        process: 'readonly',
      },
    },
    rules: {
      // React Hooks 规则
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'off',

      // React Refresh 规则
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],

      // TypeScript 规则
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'warn',

      // 其他规则
      'no-async-promise-executor': 'off',
      'no-unused-vars': 'off', // 关闭基础规则，使用 TypeScript 版本
      'no-extra-semi': 'off',

      // Prettier 规则
      'prettier/prettier': 'error',
    },
  },
]
